:root {
    /* الوضع النهاري */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --border-color: #dee2e6;
    --link-color: #0d6efd;
    --link-hover: #0a58ca;
    --table-stripe: rgba(0, 0, 0, 0.05);
    --input-bg: #fff;
    --input-border: #ced4da;
    --card-bg: #fff;
    --card-border: rgba(0, 0, 0, 0.125);
    --navbar-bg: #f8f9fa;
    --navbar-color: #212529;
    --sidebar-bg: #f8f9fa;
    --sidebar-color: #212529;
    --sidebar-hover: #e9ecef;
    --dropdown-bg: #fff;
    --dropdown-color: #212529;
    --dropdown-hover: #f8f9fa;
    --modal-bg: #fff;
    --shadow-color: rgba(0, 0, 0, 0.1);
    
    /* ألوان جديدة */
    --primary-color: #4e73df;
    --primary-hover: #2e59d9;
    --success-color: #1cc88a;
    --info-color: #36b9cc;
    --warning-color: #f6c23e;
    --danger-color: #e74a3b;
    --secondary-color: #858796;
    --light-color: #f8f9fc;
    --dark-color: #5a5c69;
}

[data-theme="dark"] {
    /* الوضع الليلي */
    --bg-primary: #1a1a2e;
    --bg-secondary: #16213e;
    --text-primary: #e6e6e6;
    --text-secondary: #adb5bd;
    --border-color: #495057;
    --link-color: #6ea8fe;
    --link-hover: #9ec5fe;
    --table-stripe: rgba(255, 255, 255, 0.05);
    --input-bg: #343a40;
    --input-border: #495057;
    --card-bg: #16213e;
    --card-border: rgba(255, 255, 255, 0.125);
    --navbar-bg: #0f3460;
    --navbar-color: #f8f9fa;
    --sidebar-bg: #16213e;
    --sidebar-color: #f8f9fa;
    --sidebar-hover: #495057;
    --dropdown-bg: #16213e;
    --dropdown-color: #f8f9fa;
    --dropdown-hover: #495057;
    --modal-bg: #16213e;
    --shadow-color: rgba(0, 0, 0, 0.5);
    
    /* ألوان جديدة */
    --primary-color: #4e73df;
    --primary-hover: #2e59d9;
    --success-color: #1cc88a;
    --info-color: #36b9cc;
    --warning-color: #f6c23e;
    --danger-color: #e74a3b;
    --secondary-color: #858796;
    --light-color: #f8f9fc;
    --dark-color: #5a5c69;
}

/* تطبيق المتغيرات */
body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-family: 'Tajawal', 'Segoe UI', sans-serif;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* الجداول */
.table {
    color: var(--text-primary);
    border-color: var(--border-color);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.25rem var(--shadow-color);
}

.table thead th {
    background-color: var(--bg-secondary);
    border-bottom: 2px solid var(--border-color);
    font-weight: 600;
    padding: 12px;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: var(--table-stripe);
}

.table tbody tr {
    transition: background-color 0.2s ease;
}

.table tbody tr:hover {
    background-color: var(--sidebar-hover);
}

/* البطاقات */
.card {
    background-color: var(--card-bg);
    border-color: var(--card-border);
    border-radius: 10px;
    box-shadow: 0 0.15rem 1.75rem 0 var(--shadow-color);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 2rem 0 var(--shadow-color);
}

.card-header {
    background-color: var(--bg-secondary);
    border-bottom-color: var(--border-color);
    padding: 1rem 1.25rem;
    font-weight: 600;
}

.card-body {
    padding: 1.25rem;
}

/* النماذج */
.form-control {
    background-color: var(--input-bg);
    border-color: var(--input-border);
    color: var(--text-primary);
    border-radius: 8px;
    padding: 0.75rem 1rem;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-control:focus {
    background-color: var(--input-bg);
    border-color: var(--primary-color);
    color: var(--text-primary);
    box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
}

/* القوائم المنسدلة */
.dropdown-menu {
    background-color: var(--dropdown-bg);
    border-color: var(--border-color);
    border-radius: 8px;
    box-shadow: 0 0.5rem 1rem var(--shadow-color);
    padding: 0.5rem 0;
}

.dropdown-item {
    color: var(--dropdown-color);
    padding: 0.5rem 1.5rem;
    transition: background-color 0.2s ease;
}

.dropdown-item:hover {
    background-color: var(--dropdown-hover);
}

/* شريط التنقل */
.navbar {
    background-color: var(--navbar-bg);
    box-shadow: 0 0.15rem 1.75rem 0 var(--shadow-color);
    padding: 1rem;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.25rem;
}

.navbar-dark .navbar-nav .nav-link {
    color: var(--navbar-color);
    padding: 0.5rem 1rem;
    transition: color 0.2s ease, background-color 0.2s ease;
    border-radius: 5px;
}

.navbar-dark .navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.navbar-dark .navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
}

/* الشريط الجانبي */
.sidebar {
    background-color: var(--sidebar-bg);
    transition: background-color 0.3s ease;
}

.sidebar .nav-link {
    color: var(--sidebar-color);
    padding: 0.75rem 1rem;
    transition: background-color 0.2s ease;
    border-radius: 5px;
    margin: 0.25rem 0;
}

.sidebar .nav-link:hover {
    background-color: var(--sidebar-hover);
}

.sidebar .nav-link.active {
    background-color: var(--primary-color);
    color: white;
}

.sidebar .nav-link i {
    margin-left: 0.5rem;
    width: 1.25rem;
    text-align: center;
}

/* الأزرار */
.btn {
    border-radius: 8px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(78, 115, 223, 0.4);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-success:hover {
    background-color: #169b6b;
    border-color: #169b6b;
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(28, 200, 138, 0.4);
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-danger:hover {
    background-color: #d52a1a;
    border-color: #d52a1a;
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(231, 74, 59, 0.4);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* الروابط */
a {
    color: var(--link-color);
    text-decoration: none;
    transition: color 0.2s ease;
}

a:hover {
    color: var(--link-hover);
}

/* النوافذ المنبثقة */
.modal-content {
    background-color: var(--modal-bg);
    color: var(--text-primary);
    border-radius: 10px;
    border: none;
    box-shadow: 0 0.5rem 2rem var(--shadow-color);
}

.modal-header {
    border-bottom-color: var(--border-color);
    padding: 1.25rem;
}

.modal-footer {
    border-top-color: var(--border-color);
    padding: 1.25rem;
}

/* الشارات */
.badge {
    border-radius: 5px;
    padding: 0.5em 0.75em;
    font-weight: 500;
}

/* مربع البحث */
.form-control-search {
    background-color: var(--input-bg);
    border-color: var(--input-border);
    color: var(--text-primary);
    border-radius: 8px;
    padding: 0.75rem 1rem;
}

/* زر تبديل الوضع */
.theme-toggle {
    background-color: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.theme-toggle:hover {
    background-color: var(--sidebar-hover);
    transform: rotate(15deg);
}

.theme-toggle i {
    font-size: 1.25rem;
}

/* التنبيهات */
.alert {
    border-radius: 10px;
    border: none;
    box-shadow: 0 0.125rem 0.25rem var(--shadow-color);
    padding: 1rem 1.25rem;
}

.alert-success {
    background-color: rgba(28, 200, 138, 0.2);
    color: var(--success-color);
}

.alert-danger {
    background-color: rgba(231, 74, 59, 0.2);
    color: var(--danger-color);
}

.alert-warning {
    background-color: rgba(246, 194, 62, 0.2);
    color: var(--warning-color);
}

.alert-info {
    background-color: rgba(54, 185, 204, 0.2);
    color: var(--info-color);
}

/* ترقيم الصفحات */
.pagination {
    border-radius: 8px;
    overflow: hidden;
}

.pagination .page-item .page-link {
    color: var(--primary-color);
    background-color: var(--bg-primary);
    border-color: var(--border-color);
    padding: 0.5rem 0.75rem;
    transition: all 0.2s ease;
}

.pagination .page-item .page-link:hover {
    background-color: var(--bg-secondary);
    z-index: 2;
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* تأثيرات الحركة */
.animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
    animation: slideUp 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }
    
    .navbar-brand {
        font-size: 1.1rem;
    }
    
    .table-responsive {
        border-radius: 8px;
        box-shadow: 0 0.125rem 0.25rem var(--shadow-color);
    }
}

/* إضافة خط Tajawal */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');

/* الجداول */
.table {
    color: var(--text-primary);
    border-color: var(--border-color);
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 0.125rem 0.25rem var(--shadow-color);
}

.table thead th {
    background-color: var(--bg-secondary);
    border-bottom: 2px solid var(--border-color);
    font-weight: 600;
    padding: 12px;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: var(--table-stripe);
}

.table tbody tr {
    transition: background-color 0.2s ease;
}

.table tbody tr:hover {
    background-color: var(--sidebar-hover);
}

/* البطاقات */
.card {
    background-color: var(--card-bg);
    border-color: var(--card-border);
    border-radius: 10px;
    box-shadow: 0 0.15rem 1.75rem 0 var(--shadow-color);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    margin-bottom: 1.5rem;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 2rem 0 var(--shadow-color);
}

.card-header {
    background-color: var(--bg-secondary);
    border-bottom-color: var(--border-color);
    padding: 1rem 1.25rem;
    font-weight: 600;
}

.card-body {
    padding: 1.25rem;
}

/* النماذج */
.form-control {
    background-color: var(--input-bg);
    border-color: var(--input-border);
    color: var(--text-primary);
    border-radius: 8px;
    padding: 0.75rem 1rem;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.form-control:focus {
    background-color: var(--input-bg);
    border-color: var(--primary-color);
    color: var(--text-primary);
    box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
}

/* القوائم المنسدلة */
.dropdown-menu {
    background-color: var(--dropdown-bg);
    border-color: var(--border-color);
    border-radius: 8px;
    box-shadow: 0 0.5rem 1rem var(--shadow-color);
    padding: 0.5rem 0;
}

.dropdown-item {
    color: var(--dropdown-color);
    padding: 0.5rem 1.5rem;
    transition: background-color 0.2s ease;
}

.dropdown-item:hover {
    background-color: var(--dropdown-hover);
}

/* شريط التنقل */
.navbar {
    background-color: var(--navbar-bg);
    box-shadow: 0 0.15rem 1.75rem 0 var(--shadow-color);
    padding: 1rem;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.25rem;
}

.navbar-dark .navbar-nav .nav-link {
    color: var(--navbar-color);
    padding: 0.5rem 1rem;
    transition: color 0.2s ease, background-color 0.2s ease;
    border-radius: 5px;
}

.navbar-dark .navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.navbar-dark .navbar-nav .nav-link.active {
    background-color: rgba(255, 255, 255, 0.2);
}

/* الشريط الجانبي */
.sidebar {
    background-color: var(--sidebar-bg);
    transition: background-color 0.3s ease;
}

.sidebar .nav-link {
    color: var(--sidebar-color);
    padding: 0.75rem 1rem;
    transition: background-color 0.2s ease;
    border-radius: 5px;
    margin: 0.25rem 0;
}

.sidebar .nav-link:hover {
    background-color: var(--sidebar-hover);
}

.sidebar .nav-link.active {
    background-color: var(--primary-color);
    color: white;
}

.sidebar .nav-link i {
    margin-left: 0.5rem;
    width: 1.25rem;
    text-align: center;
}

/* الأزرار */
.btn {
    border-radius: 8px;
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(78, 115, 223, 0.4);
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-success:hover {
    background-color: #169b6b;
    border-color: #169b6b;
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(28, 200, 138, 0.4);
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-danger:hover {
    background-color: #d52a1a;
    border-color: #d52a1a;
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(231, 74, 59, 0.4);
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* الروابط */
a {
    color: var(--link-color);
    text-decoration: none;
    transition: color 0.2s ease;
}

a:hover {
    color: var(--link-hover);
}

/* النوافذ المنبثقة */
.modal-content {
    background-color: var(--modal-bg);
    color: var(--text-primary);
    border-radius: 10px;
    border: none;
    box-shadow: 0 0.5rem 2rem var(--shadow-color);
}

.modal-header {
    border-bottom-color: var(--border-color);
    padding: 1.25rem;
}

.modal-footer {
    border-top-color: var(--border-color);
    padding: 1.25rem;
}

/* الشارات */
.badge {
    border-radius: 5px;
    padding: 0.5em 0.75em;
    font-weight: 500;
}

/* مربع البحث */
.form-control-search {
    background-color: var(--input-bg);
    border-color: var(--input-border);
    color: var(--text-primary);
    border-radius: 8px;
    padding: 0.75rem 1rem;
}

/* زر تبديل الوضع */
.theme-toggle {
    background-color: transparent;
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.theme-toggle:hover {
    background-color: var(--sidebar-hover);
    transform: rotate(15deg);
}

.theme-toggle i {
    font-size: 1.25rem;
}

/* التنبيهات */
.alert {
    border-radius: 10px;
    border: none;
    box-shadow: 0 0.125rem 0.25rem var(--shadow-color);
    padding: 1rem 1.25rem;
}

.alert-success {
    background-color: rgba(28, 200, 138, 0.2);
    color: var(--success-color);
}

.alert-danger {
    background-color: rgba(231, 74, 59, 0.2);
    color: var(--danger-color);
}

.alert-warning {
    background-color: rgba(246, 194, 62, 0.2);
    color: var(--warning-color);
}

.alert-info {
    background-color: rgba(54, 185, 204, 0.2);
    color: var(--info-color);
}

/* ترقيم الصفحات */
.pagination {
    border-radius: 8px;
    overflow: hidden;
}

.pagination .page-item .page-link {
    color: var(--primary-color);
    background-color: var(--bg-primary);
    border-color: var(--border-color);
    padding: 0.5rem 0.75rem;
    transition: all 0.2s ease;
}

.pagination .page-item .page-link:hover {
    background-color: var(--bg-secondary);
    z-index: 2;
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
    color: white;
}

/* تأثيرات الحركة */
.animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
    animation: slideUp 0.5s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }
    
    .navbar-brand {
        font-size: 1.1rem;
    }
    
    .table-responsive {
        border-radius: 8px;
        box-shadow: 0 0.125rem 0.25rem var(--shadow-color);
    }
}

/* إضافة خط Tajawal */
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@400;500;700&display=swap');

.dashboard-stats {
    text-align: center;
    padding: 1.5rem;
}

.dashboard-stats i {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: #0d6efd;
}

.dashboard-stats h3 {
    margin-bottom: 0.5rem;
}

.search-form {
    margin-bottom: 1.5rem;
}

.map-container {
    height: 600px;
    margin-bottom: 1.5rem;
}

.activity-log-item {
    padding: 1rem;
    border-bottom: 1px solid #dee2e6;
}

.activity-log-item:last-child {
    border-bottom: none;
}

.activity-log-time {
    color: #6c757d;
    font-size: 0.875rem;
}

.required:after {
    content: " *";
    color: red;
}

/* الوضع الليلي */
[data-theme="dark"] {
    --bg-color: #1a1a1a;
    --text-color: #ffffff;
    --card-bg: #2d2d2d;
    --border-color: #404040;
    --hover-bg: #3a3a3a;
}

[data-theme="dark"] body {
    background-color: var(--bg-color);
    color: var(--text-color);
}

[data-theme="dark"] .card {
    background-color: var(--card-bg);
    border-color: var(--border-color);
}

[data-theme="dark"] .table {
    color: var(--text-color);
}

[data-theme="dark"] .table-striped > tbody > tr:nth-of-type(odd) {
    background-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .modal-content {
    background-color: var(--card-bg);
    color: var(--text-color);
}

[data-theme="dark"] .form-control,
[data-theme="dark"] .form-select {
    background-color: var(--bg-color);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-theme="dark"] .form-control:focus,
[data-theme="dark"] .form-select:focus {
    background-color: var(--bg-color);
    color: var(--text-color);
}

[data-theme="dark"] .navbar {
    background-color: var(--card-bg) !important;
}

[data-theme="dark"] .card-header {
    background-color: var(--card-bg);
    border-bottom-color: var(--border-color);
}

[data-theme="dark"] .activity-log-item {
    border-color: var(--border-color);
}

[data-theme="dark"] .btn-close {
    filter: invert(1) grayscale(100%) brightness(200%);
}

/* زر تبديل الوضع */
.theme-switch {
    position: relative;
    width: 60px;
    height: 30px;
    margin: 0 15px;
}

.theme-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 30px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 22px;
    width: 22px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider:before {
    transform: translateX(30px);
}

.theme-switch i {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1;
    color: #ffffff;
    font-size: 14px;
}

.theme-switch .bi-sun {
    left: 8px;
}

.theme-switch .bi-moon {
    right: 8px;
}
