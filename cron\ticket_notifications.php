#!/usr/bin/env php
<?php
/**
 * مهمة Cron لإرسال إشعارات التذاكر اليومية
 * يتم تشغيلها يومياً لإرسال:
 * 1. التقرير اليومي للتذاكر
 * 2. تذكير بالتذاكر المفتوحة لفترة طويلة
 */

// تعيين المسار الأساسي
define('BASE_PATH', dirname(__DIR__));

// تضمين الملفات المطلوبة
require_once BASE_PATH . '/includes/init.php';
require_once BASE_PATH . '/includes/ticket_notifications.php';

// دالة تسجيل الأحداث
function log_ticket_cron($message) {
    $log_file = BASE_PATH . '/logs/ticket_notifications.log';
    $timestamp = date('Y-m-d H:i:s');
    $log_message = "[$timestamp] $message\n";
    
    // التأكد من وجود مجلد السجلات
    if (!is_dir(dirname($log_file))) {
        mkdir(dirname($log_file), 0755, true);
    }
    
    file_put_contents($log_file, $log_message, FILE_APPEND);
}

try {
    log_ticket_cron("بدء مهمة إشعارات التذاكر اليومية");
    
    // التحقق من وجود جدول التذاكر
    $table_check = $conn->query("SHOW TABLES LIKE 'tickets'");
    if (!$table_check || $table_check->num_rows == 0) {
        log_ticket_cron("جدول التذاكر غير موجود - تم إنهاء المهمة");
        exit();
    }
    
    // 1. إرسال التقرير اليومي
    log_ticket_cron("إرسال التقرير اليومي للتذاكر");
    $daily_report_result = sendDailyTicketReport($conn);
    
    if ($daily_report_result) {
        log_ticket_cron("تم إرسال التقرير اليومي بنجاح");
    } else {
        log_ticket_cron("فشل في إرسال التقرير اليومي");
    }
    
    // 2. البحث عن التذاكر المفتوحة لفترة طويلة (أكثر من 7 أيام)
    $overdue_days = 7;
    $overdue_query = "SELECT t.*, a.agent_name, u.full_name as created_by_name,
                             DATEDIFF(NOW(), t.created_at) as days_open
                      FROM tickets t
                      LEFT JOIN agents a ON t.agent_id = a.id
                      LEFT JOIN users u ON t.created_by = u.id
                      WHERE t.status NOT IN ('resolved', 'closed', 'rejected')
                      AND DATEDIFF(NOW(), t.created_at) >= ?
                      ORDER BY t.created_at ASC";
    
    $overdue_stmt = $conn->prepare($overdue_query);
    $overdue_stmt->bind_param("i", $overdue_days);
    $overdue_stmt->execute();
    $overdue_result = $overdue_stmt->get_result();
    
    if ($overdue_result->num_rows > 0) {
        $overdue_tickets = [];
        while ($ticket = $overdue_result->fetch_assoc()) {
            $overdue_tickets[] = $ticket;
        }
        
        log_ticket_cron("تم العثور على " . count($overdue_tickets) . " تذكرة مفتوحة لأكثر من $overdue_days أيام");
        
        // إرسال إشعار التذاكر المتأخرة
        $overdue_result = notifyOverdueTickets($overdue_tickets);
        
        if ($overdue_result) {
            log_ticket_cron("تم إرسال إشعار التذاكر المتأخرة بنجاح");
        } else {
            log_ticket_cron("فشل في إرسال إشعار التذاكر المتأخرة");
        }
    } else {
        log_ticket_cron("لا توجد تذاكر مفتوحة لأكثر من $overdue_days أيام");
    }
    
    // 3. إحصائيات إضافية للتذاكر عالية الأولوية المفتوحة
    $urgent_query = "SELECT COUNT(*) as urgent_count
                     FROM tickets 
                     WHERE priority = 'urgent' 
                     AND status NOT IN ('resolved', 'closed', 'rejected')";
    
    $urgent_result = $conn->query($urgent_query);
    $urgent_count = $urgent_result->fetch_assoc()['urgent_count'];
    
    if ($urgent_count > 0) {
        log_ticket_cron("يوجد $urgent_count تذكرة عاجلة مفتوحة");
        
        // إرسال تنبيه خاص للتذاكر العاجلة
        $urgent_message = "🚨 <b>تنبيه: تذاكر عاجلة مفتوحة</b>\n\n";
        $urgent_message .= "📊 <b>عدد التذاكر العاجلة المفتوحة:</b> $urgent_count\n";
        $urgent_message .= "⚠️ <b>يرجى مراجعة هذه التذاكر فوراً</b>";
        
        $urgent_notification_result = sendTicketTelegramNotification($urgent_message);
        
        if ($urgent_notification_result) {
            log_ticket_cron("تم إرسال تنبيه التذاكر العاجلة بنجاح");
        } else {
            log_ticket_cron("فشل في إرسال تنبيه التذاكر العاجلة");
        }
    } else {
        log_ticket_cron("لا توجد تذاكر عاجلة مفتوحة");
    }
    
    log_ticket_cron("انتهت مهمة إشعارات التذاكر اليومية بنجاح");
    
} catch (Exception $e) {
    log_ticket_cron("خطأ في مهمة إشعارات التذاكر: " . $e->getMessage());
    
    // إرسال إشعار خطأ
    try {
        $error_message = "❌ <b>خطأ في نظام إشعارات التذاكر</b>\n\n";
        $error_message .= "📅 الوقت: " . date('Y-m-d H:i:s') . "\n";
        $error_message .= "🔍 الخطأ: " . $e->getMessage();
        
        sendTicketTelegramNotification($error_message);
    } catch (Exception $notification_error) {
        log_ticket_cron("فشل في إرسال إشعار الخطأ: " . $notification_error->getMessage());
    }
} finally {
    if (isset($conn)) {
        $conn->close();
    }
}
?>
