<?php
session_start();

// تسجيل النشاط قبل تدمير الجلسة (اختياري)
if (isset($_SESSION['user_id'])) {
    try {
        // تضمين ملف قاعدة البيانات
        $config_path = __DIR__ . '/../config/database.php';
        if (file_exists($config_path)) {
            require_once $config_path;

            $user_id = $_SESSION['user_id'];
            $action = "تسجيل خروج";
            $description = "تم تسجيل الخروج من النظام";

            // التحقق من وجود الجدول
            $check_table = $conn->query("SHOW TABLES LIKE 'activity_log'");
            if ($check_table && $check_table->num_rows > 0) {
                $stmt = $conn->prepare("INSERT INTO activity_log (user_id, action, description) VALUES (?, ?, ?)");
                if ($stmt) {
                    $stmt->bind_param("iss", $user_id, $action, $description);
                    $stmt->execute();
                    $stmt->close();
                }
            }
        }
    } catch (Exception $e) {
        // تجاهل أخطاء تسجيل النشاط - لا نريد أن توقف عملية تسجيل الخروج
    }
}

// تدمير الجلسة
session_destroy();

// إعادة التوجيه
header("Location: ../index.php");
exit();
?>
