<?php
require_once '../includes/init.php';

// التحقق من تسجيل الدخول
check_login();

if (isset($_GET['file'])) {
    $filename = basename($_GET['file']);
    $file = dirname(__DIR__) . '/backups/' . $filename;
    
    if (file_exists($file)) {
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Length: ' . filesize($file));
        readfile($file);
        exit;
    }
}

// إذا لم يتم العثور على الملف، إعادة التوجيه إلى صفحة النسخ الاحتياطي
header('Location: backup.php');
exit;
