    </div>
    <script src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.7/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="../assets/js/main.js"></script>
    
    <!-- إضافة CSS لـ DataTables -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
    
    <!-- تفعيل الوضع المظلم -->
    <script>
        // التحقق من وجود تفضيل الوضع المظلم في localStorage
        if (localStorage.getItem('theme') === 'dark') {
            document.documentElement.setAttribute('data-theme', 'dark');
        }
        
        // إضافة زر تبديل الوضع المظلم في شريط التنقل إذا لم يكن موجودًا
        document.addEventListener('DOMContentLoaded', function() {
            if (!document.querySelector('.theme-toggle')) {
                const navbarNav = document.querySelector('.navbar-nav.me-auto');
                if (navbarNav) {
                    const themeToggleItem = document.createElement('li');
                    themeToggleItem.className = 'nav-item ms-2';
                    themeToggleItem.innerHTML = `
                        <button class="btn btn-sm theme-toggle" id="theme-toggle">
                            <i class="bi bi-moon-stars"></i>
                        </button>
                    `;
                    navbarNav.appendChild(themeToggleItem);
                    
                    // إضافة حدث النقر لزر تبديل الوضع
                    document.getElementById('theme-toggle').addEventListener('click', function() {
                        if (document.documentElement.getAttribute('data-theme') === 'dark') {
                            document.documentElement.removeAttribute('data-theme');
                            localStorage.setItem('theme', 'light');
                            this.innerHTML = '<i class="bi bi-moon-stars"></i>';
                        } else {
                            document.documentElement.setAttribute('data-theme', 'dark');
                            localStorage.setItem('theme', 'dark');
                            this.innerHTML = '<i class="bi bi-sun"></i>';
                        }
                    });
                    
                    // تعيين الأيقونة الصحيحة عند التحميل
                    if (localStorage.getItem('theme') === 'dark') {
                        document.getElementById('theme-toggle').innerHTML = '<i class="bi bi-sun"></i>';
                    }
                }
            }
        });
    </script>
</body>
</html>
