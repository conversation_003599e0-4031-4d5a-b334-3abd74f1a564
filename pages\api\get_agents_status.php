<?php
require_once '../../includes/init.php';

$query = "SELECT 
    a.id,
    a.agent_name,
    ai.ip_address,
    a.last_status as status,
    ai.last_ping_time,
    (
        SELECT AVG(CASE WHEN response_time IS NOT NULL THEN response_time ELSE 0 END)
        FROM agent_status_logs
        WHERE agent_id = a.id
        AND changed_at >= DATE_SUB(NOW(), INTERVAL 5 MINUTE)
    ) as response_time,
    a.last_status_change as last_update,
    (
        SELECT (successful_checks * 100.0 / NULLIF(total_checks, 0))
        FROM agent_uptime_stats
        WHERE agent_id = a.id
        AND date_recorded = CURDATE()
    ) as uptime
FROM agents a
JOIN agent_ips ai ON a.id = ai.agent_id
WHERE ai.is_primary = 1
ORDER BY a.agent_name";

$result = $conn->query($query);
$data = [];

while ($row = $result->fetch_assoc()) {
    $data[] = [
        'agent_id' => $row['id'],
        'agent_name' => $row['agent_name'],
        'ip_address' => $row['ip_address'],
        'status' => $row['status'],
        'response_time' => $row['response_time'],
        'last_check' => $row['last_update'],
        'uptime' => $row['uptime']
    ];
}

header('Content-Type: application/json');
echo json_encode($data);
