<?php
require_once '../../includes/init.php';

// استعلام لجلب سجل التغييرات
$query = "SELECT 
    l.changed_at as log_time,
    a.agent_name,
    l.old_status,
    l.new_status,
    l.response_time
FROM agent_status_logs l
JOIN agents a ON l.agent_id = a.id
ORDER BY l.changed_at DESC
LIMIT 100";

$result = $conn->query($query);
$data = [];

while ($row = $result->fetch_assoc()) {
    $data[] = $row;
}

header('Content-Type: application/json');
echo json_encode($data);
