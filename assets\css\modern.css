/* تحديث واجهة الموقع - ملف modern.css */

/* تحسينات عامة */
:root {
    /* ألوان أساسية محسنة */
    --primary-color: #4361ee;
    --primary-hover: #3a56d4;
    --success-color: #2ecc71;
    --info-color: #3498db;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --secondary-color: #6c757d;
    
    /* ألوان الخلفية والنص */
    --bg-primary: #f9fafb;
    --bg-secondary: #f1f5f9;
    --text-primary: #1e293b;
    --text-secondary: #64748b;
    
    /* ألوان إضافية */
    --card-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --hover-transition: all 0.3s ease;
    --border-radius: 12px;
}

[data-theme="dark"] {
    /* ألوان الوضع الليلي المحسنة */
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --text-primary: #f1f5f9;
    --text-secondary: #cbd5e1;
    --card-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
}

/* تحسينات الجسم */
body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-family: 'Tajawal', sans-serif;
    line-height: 1.6;
}

/* تحسينات البطاقات */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--card-shadow);
    transition: var(--hover-transition);
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.card-header {
    background-color: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.25rem 1.5rem;
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

/* تحسينات بطاقات الإحصائيات */
.dashboard-card {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--card-shadow);
    transition: var(--hover-transition);
}

.dashboard-card:hover {
    transform: translateY(-5px);
}

.icon-shape {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* تحسينات الأزرار */
.btn {
    border-radius: 8px;
    padding: 0.6rem 1.2rem;
    font-weight: 500;
    transition: var(--hover-transition);
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover, .btn-primary:focus {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
}

/* تحسينات الجداول */
.table-custom {
    border-radius: var(--border-radius);
    overflow: hidden;
    box-shadow: var(--card-shadow);
}

.table-custom thead th {
    background-color: var(--bg-secondary);
    border-bottom: none;
    padding: 1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.table-custom tbody tr {
    transition: var(--hover-transition);
}

.table-custom tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

/* تحسينات النماذج */
.form-control, .form-select {
    border-radius: 8px;
    padding: 0.75rem 1rem;
    border: 1px solid rgba(0, 0, 0, 0.1);
    transition: var(--hover-transition);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--text-secondary);
}

/* تحسينات شريط التنقل */
.navbar {
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    padding: 0.75rem 1.5rem;
}

.navbar-brand {
    font-weight: 700;
}

/* تحسينات الشارات */
.status-badge {
    padding: 0.4rem 0.8rem;
    border-radius: 6px;
    font-weight: 500;
    font-size: 0.85rem;
    display: inline-block;
}

.status-active {
    background-color: rgba(46, 204, 113, 0.15);
    color: #2ecc71;
}

.status-inactive {
    background-color: rgba(231, 76, 60, 0.15);
    color: #e74c3c;
}

/* تحسينات فلاتر البحث */
#searchFilters {
    padding: 1.5rem;
}

/* تحسينات الرسوم المتحركة */
.animate__animated {
    animation-duration: 0.6s;
}

/* تأثيرات إضافية */
.cell-primary {
    font-weight: 600;
}

.cell-action {
    width: 120px;
    text-align: center;
}

.cell-shrink {
    width: 60px;
}

/* تحسينات للأجهزة المحمولة */
@media (max-width: 768px) {
    .card {
        margin-bottom: 1rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
    }
    
    .table-responsive {
        border-radius: var(--border-radius);
        box-shadow: var(--card-shadow);
    }
}
