<?php
// تعديل إعدادات الجلسة لإلغاء تسجيل الخروج التلقائي
// يجب تعيين هذه الإعدادات قبل بدء الجلسة
if (session_status() === PHP_SESSION_NONE) {
    // تعيين مدة الجلسة إلى سنة كاملة (31536000 ثانية)
    ini_set('session.gc_maxlifetime', 31536000);

    // تعيين مدة صلاحية ملف الكوكيز إلى سنة كاملة
    ini_set('session.cookie_lifetime', 31536000);

    // بدء الجلسة مع تعيين مدة صلاحية الكوكيز
    session_start([
        'cookie_lifetime' => 31536000,
        'gc_maxlifetime' => 31536000
    ]);
}

require_once dirname(__DIR__) . '/config/database.php';

// تم حذف نظام الصلاحيات

// تعريف الثوابت
define('SITE_URL', 'http://localhost/agents_management');
define('ROOT_PATH', dirname(__DIR__));

// دوال المساعدة
function clean($string) {
    global $conn;
    return htmlspecialchars(strip_tags($conn->real_escape_string($string)));
}

function redirect($url) {
    header("Location: " . $url);
    exit();
}

// التحقق من تسجيل الدخول
function check_login() {
    if (!isset($_SESSION['user_id'])) {
        $_SESSION['error'] = "يرجى تسجيل الدخول أولاً";
        redirect(SITE_URL . '/index.php');
    }
}

// دالة مساعدة للحصول على المسار الكامل
function get_path($path) {
    return SITE_URL . $path;
}

// دالة مساعدة للتحقق من الصفحة الحالية
function is_active_page($page) {
    return strpos($_SERVER['PHP_SELF'], $page) !== false ? 'active' : '';
}

// تسجيل النشاط
function log_activity($action, $description) {
    global $conn;

    try {
        $user_id = $_SESSION['user_id'] ?? null;

        // إدراج السجل مباشرة
        $stmt = $conn->prepare("INSERT INTO activity_log (user_id, action, description) VALUES (?, ?, ?)");
        $stmt->bind_param("iss", $user_id, $action, $description);
        $stmt->execute();
        $stmt->close();

    } catch (Exception $e) {
        // في حالة الخطأ، لا نوقف تنفيذ الكود
        error_log("خطأ في تسجيل النشاط: " . $e->getMessage());
    }
}

// التحقق من الصلاحيات - مبسط بعد حذف نظام الصلاحيات
function check_permission($permission) {
    // المدير لديه كل الصلاحيات
    if (isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin') {
        return true;
    }

    // السماح للمستخدمين العاديين بالوصول للوظائف الأساسية
    return true;
}

// التحقق من صلاحيات المستخدم للصفحة الحالية - مبسط بعد حذف نظام الصلاحيات
function check_page_permissions() {
    // السماح بالوصول لجميع المستخدمين المسجلين
    // يمكن إضافة قيود إضافية هنا إذا لزم الأمر
    return;
}

// دالة بديلة بسيطة لفحص الصلاحيات - تسمح بالوصول للجميع
function currentUserHasPermission($permission) {
    // المدير لديه كل الصلاحيات
    if (isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin') {
        return true;
    }

    // السماح للمستخدمين العاديين بالوصول للوظائف الأساسية
    return true;
}

// دالة بديلة لـ requirePermission
function requirePermission($permission) {
    // لا نحتاج للتحقق من الصلاحيات بعد الآن
    return;
}
?>
