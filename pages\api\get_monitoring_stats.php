<?php
require_once '../../includes/init.php';

header('Content-Type: application/json');

try {
    // إحصائيات المشتركين
    $stats_query = "SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN last_status = 'online' THEN 1 ELSE 0 END) as online,
        SUM(CASE WHEN last_status = 'offline' THEN 1 ELSE 0 END) as offline
    FROM agents
    WHERE is_active = 1";

    $result = $conn->query($stats_query);
    if (!$result) {
        throw new Exception("Error in stats query: " . $conn->error);
    }
    $stats = $result->fetch_assoc();

    // متوسط التوفر
    $uptime_query = "SELECT 
        COALESCE(AVG(successful_checks * 100.0 / NULLIF(total_checks, 0)), 0) as avg_uptime
    FROM agent_uptime_stats
    WHERE date_recorded = CURDATE()";

    $result = $conn->query($uptime_query);
    if (!$result) {
        throw new Exception("Error in uptime query: " . $conn->error);
    }
    $uptime = $result->fetch_assoc();

    $response = [
        'total_agents' => (int)$stats['total'],
        'online_agents' => (int)$stats['online'],
        'offline_agents' => (int)$stats['offline'],
        'avg_uptime' => round($uptime['avg_uptime'], 2)
    ];

    echo json_encode($response);
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}
