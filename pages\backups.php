<?php
require_once '../includes/header.php';
require_once '../includes/backup.php';

// إنشاء كائن النسخ الاحتياطي
$backup = new DatabaseBackup($conn);

// معالجة طلب النسخ الاحتياطي
if (isset($_POST['create_backup'])) {
    $result = $backup->backup();
    if ($result['success']) {
        $success = $result['message'];
    } else {
        $error = $result['message'];
    }
}

// معالجة طلب التحميل
if (isset($_GET['download'])) {
    $file = '../backups/' . basename($_GET['download']);
    if (file_exists($file)) {
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . basename($file) . '"');
        header('Content-Length: ' . filesize($file));
        readfile($file);
        exit;
    }
}

// جلب قائمة النسخ الاحتياطية
$backups = $backup->getBackupsList();
?>

<div class="container-fluid py-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>إدارة النسخ الاحتياطي</h2>
        <form method="post" class="d-inline">
            <button type="submit" name="create_backup" class="btn btn-primary">
                <i class="bi bi-download"></i> إنشاء نسخة احتياطية جديدة
            </button>
        </form>
    </div>

    <?php if (isset($success)): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?php echo $success; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <?php if (isset($error)): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">النسخ الاحتياطية المتوفرة</h5>
        </div>
        <div class="card-body">
            <?php if (empty($backups)): ?>
                <p class="text-muted">لا توجد نسخ احتياطية حالياً</p>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>اسم الملف</th>
                                <th>حجم الملف</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($backups as $backup): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($backup['name']); ?></td>
                                    <td><?php echo htmlspecialchars($backup['size']); ?></td>
                                    <td><?php echo htmlspecialchars($backup['date']); ?></td>
                                    <td>
                                        <a href="?download=<?php echo urlencode($backup['name']); ?>" 
                                           class="btn btn-success btn-sm">
                                            <i class="bi bi-download"></i> تحميل
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <div class="card mt-4">
        <div class="card-header">
            <h5 class="card-title mb-0">معلومات النسخ الاحتياطي</h5>
        </div>
        <div class="card-body">
            <ul class="list-unstyled">
                <li><i class="bi bi-info-circle"></i> يتم الاحتفاظ بآخر 5 نسخ احتياطية فقط.</li>
                <li><i class="bi bi-info-circle"></i> يتم تسمية الملفات تلقائياً بالتاريخ والوقت.</li>
                <li><i class="bi bi-info-circle"></i> يمكنك تحميل أي نسخة احتياطية في أي وقت.</li>
                <li><i class="bi bi-exclamation-triangle text-warning"></i> يُنصح بتحميل النسخ الاحتياطية وحفظها في مكان آمن.</li>
            </ul>
        </div>
    </div>
</div>

<?php require_once '../includes/footer.php'; ?>
