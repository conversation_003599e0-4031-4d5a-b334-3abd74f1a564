<?php
require_once '../includes/init.php';

// التحقق من تسجيل الدخول
if (!isset($_SESSION['user_id'])) {
    redirect(SITE_URL . '/index.php');
}

// جلب معلومات المستخدم
$user_id = $_SESSION['user_id'];
$sql = "SELECT * FROM users WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $user_id);
$stmt->execute();
$user = $stmt->get_result()->fetch_assoc();

// معالجة تحديث المعلومات الشخصية
if (isset($_POST['update_profile'])) {
    $name = clean($_POST['name']);
    $email = clean($_POST['email']);
    $phone = clean($_POST['phone']);
    
    $sql = "UPDATE users SET full_name = ?, email = ?, phone = ? WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->bind_param("sssi", $name, $email, $phone, $user_id);
    
    if ($stmt->execute()) {
        log_activity("تحديث الملف الشخصي", "تم تحديث المعلومات الشخصية");
        $success = "تم تحديث المعلومات الشخصية بنجاح";
        // تحديث معلومات المستخدم في الجلسة
        $_SESSION['user_name'] = $name;
        // تحديث معلومات المستخدم المعروضة
        $user['full_name'] = $name;
        $user['email'] = $email;
        $user['phone'] = $phone;
    } else {
        $error = "حدث خطأ أثناء تحديث المعلومات";
    }
}

// معالجة تغيير كلمة المرور
if (isset($_POST['change_password'])) {
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];
    
    // التحقق من كلمة المرور الحالية
    if (!password_verify($current_password, $user['password'])) {
        $password_error = "كلمة المرور الحالية غير صحيحة";
    }
    // التحقق من تطابق كلمة المرور الجديدة
    elseif ($new_password !== $confirm_password) {
        $password_error = "كلمة المرور الجديدة غير متطابقة";
    }
    // التحقق من طول كلمة المرور
    elseif (strlen($new_password) < 6) {
        $password_error = "يجب أن تكون كلمة المرور 6 أحرف على الأقل";
    }
    else {
        // تحديث كلمة المرور
        $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
        $sql = "UPDATE users SET password = ? WHERE id = ?";
        $stmt = $conn->prepare($sql);
        $stmt->bind_param("si", $hashed_password, $user_id);
        
        if ($stmt->execute()) {
            log_activity("تغيير كلمة المرور", "تم تغيير كلمة المرور");
            $password_success = "تم تغيير كلمة المرور بنجاح";
        } else {
            $password_error = "حدث خطأ أثناء تغيير كلمة المرور";
        }
    }
}

require_once '../includes/header.php';
?>

<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12 col-xl-8 mb-4">
            <div class="card">
                <div class="card-header pb-0">
                    <h6>المعلومات الشخصية</h6>
                </div>
                <div class="card-body">
                    <?php if (isset($success)): ?>
                        <div class="alert alert-success"><?php echo $success; ?></div>
                    <?php endif; ?>
                    <?php if (isset($error)): ?>
                        <div class="alert alert-danger"><?php echo $error; ?></div>
                    <?php endif; ?>

                    <form method="POST" action="">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">الاسم</label>
                                    <input type="text" name="name" class="form-control" value="<?php echo htmlspecialchars($user['full_name']); ?>" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" name="email" class="form-control" value="<?php echo htmlspecialchars($user['email']); ?>" required>
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">رقم الهاتف</label>
                            <input type="text" name="phone" class="form-control" value="<?php echo htmlspecialchars($user['phone'] ?? ''); ?>">
                        </div>
                        <div class="text-end">
                            <button type="submit" name="update_profile" class="btn btn-primary">
                                <i class="bi bi-save"></i> حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-12 col-xl-4 mb-4">
            <div class="card">
                <div class="card-header pb-0">
                    <h6>تغيير كلمة المرور</h6>
                </div>
                <div class="card-body">
                    <?php if (isset($password_success)): ?>
                        <div class="alert alert-success"><?php echo $password_success; ?></div>
                    <?php endif; ?>
                    <?php if (isset($password_error)): ?>
                        <div class="alert alert-danger"><?php echo $password_error; ?></div>
                    <?php endif; ?>

                    <form method="POST" action="">
                        <div class="mb-3">
                            <label class="form-label">كلمة المرور الحالية</label>
                            <input type="password" name="current_password" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">كلمة المرور الجديدة</label>
                            <input type="password" name="new_password" class="form-control" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">تأكيد كلمة المرور الجديدة</label>
                            <input type="password" name="confirm_password" class="form-control" required>
                        </div>
                        <div class="text-end">
                            <button type="submit" name="change_password" class="btn btn-warning">
                                <i class="bi bi-key"></i> تغيير كلمة المرور
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- بطاقة معلومات الحساب -->
            <div class="card mt-4">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="avatar avatar-xl bg-gradient-primary rounded-circle">
                            <i class="bi bi-person-circle fs-2 text-white"></i>
                        </div>
                        <div class="ms-3">
                            <h6 class="mb-0"><?php echo htmlspecialchars($user['full_name']); ?></h6>
                            <p class="text-sm text-muted mb-0">مدير النظام</p>
                        </div>
                    </div>
                    <hr class="horizontal dark">
                    <div class="row">
                        <div class="col-6">
                            <p class="text-sm mb-0">
                                <i class="bi bi-envelope me-1"></i>
                                <?php echo htmlspecialchars($user['email']); ?>
                            </p>
                        </div>
                        <div class="col-6">
                            <p class="text-sm mb-0">
                                <i class="bi bi-phone me-1"></i>
                                <?php echo htmlspecialchars($user['phone'] ?? 'غير محدد'); ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.avatar {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>

<?php require_once '../includes/footer.php'; ?>
