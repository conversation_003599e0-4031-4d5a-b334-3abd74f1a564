<?php
// تضمين ملف التهيئة
require_once '../includes/init.php';

// تسجيل الخطوات للتتبع
error_log("1. تم تضمين ملف init.php");

// التحقق من تسجيل الدخول
check_login();
error_log("2. تم التحقق من تسجيل الدخول");

// التحقق من الصلاحيات - فقط المدير يمكنه حذف المشتركين
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    $_SESSION['error'] = "ليس لديك صلاحية لحذف المشتركين";
    error_log("3. خطأ في الصلاحيات: " . $_SESSION['user_role']);
    header('Location: agents.php');
    exit();
}
error_log("3. تم التحقق من الصلاحيات");

// التحقق من وجود معرف المشترك
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error'] = "معرف المشترك غير صحيح";
    error_log("4. خطأ: معرف المشترك غير صحيح");
    header('Location: agents.php');
    exit();
}

$agent_id = intval($_GET['id']);
error_log("4. معرف المشترك: " . $agent_id);

// التحقق من وجود المشترك
$check_sql = "SELECT id FROM agents WHERE id = ?";
$check_stmt = $conn->prepare($check_sql);

if (!$check_stmt) {
    error_log("5. خطأ في إعداد الاستعلام: " . $conn->error);
    $_SESSION['error'] = "حدث خطأ في الاستعلام: " . $conn->error;
    header('Location: agents.php');
    exit();
}

$check_stmt->bind_param('i', $agent_id);
$check_stmt->execute();
$check_result = $check_stmt->get_result();

if ($check_result->num_rows === 0) {
    $_SESSION['error'] = "المشترك غير موجود";
    error_log("6. خطأ: المشترك غير موجود");
    header('Location: agents.php');
    exit();
}
error_log("5. تم التحقق من وجود المشترك");

// بدء المعاملة
$conn->begin_transaction();
error_log("6. تم بدء المعاملة");

try {
    // حذف عناوين IP المرتبطة بالمشترك أولاً
    $ip_stmt = $conn->prepare("DELETE FROM agent_ips WHERE agent_id = ?");
    if (!$ip_stmt) {
        throw new Exception("خطأ في إعداد استعلام حذف عناوين IP: " . $conn->error);
    }
    
    $ip_stmt->bind_param('i', $agent_id);
    $ip_result = $ip_stmt->execute();
    
    if (!$ip_result) {
        throw new Exception("فشل في حذف عناوين IP: " . $ip_stmt->error);
    }
    
    error_log("7. تم حذف عناوين IP: " . $ip_stmt->affected_rows . " صفوف");
    $ip_stmt->close();

    // حذف المشترك
    $agent_stmt = $conn->prepare("DELETE FROM agents WHERE id = ?");
    if (!$agent_stmt) {
        throw new Exception("خطأ في إعداد استعلام حذف المشترك: " . $conn->error);
    }
    
    $agent_stmt->bind_param('i', $agent_id);
    $agent_result = $agent_stmt->execute();
    
    if (!$agent_result) {
        throw new Exception("فشل في حذف المشترك: " . $agent_stmt->error);
    }
    
    error_log("8. تم حذف المشترك: " . $agent_stmt->affected_rows . " صفوف");
    $agent_stmt->close();

    // تأكيد المعاملة
    $conn->commit();
    error_log("9. تم تأكيد المعاملة");
    
    // إضافة رسالة نجاح
    $_SESSION['success'] = "تم حذف المشترك بنجاح";
    
} catch (Exception $e) {
    // التراجع عن المعاملة في حالة الخطأ
    $conn->rollback();
    error_log("خطأ: " . $e->getMessage());
    $_SESSION['error'] = "حدث خطأ أثناء حذف المشترك: " . $e->getMessage();
}

// إعادة التوجيه إلى صفحة المشتركين
error_log("10. إعادة التوجيه إلى صفحة المشتركين");
header('Location: agents.php');
exit();
?>
