<?php
require_once '../includes/init.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['agent_id'])) {
    try {
        $agent_id = (int)$_POST['agent_id'];
        
        $stmt = $conn->prepare("DELETE FROM simple_agents WHERE id = ?");
        $stmt->bind_param("i", $agent_id);
        
        if ($stmt->execute()) {
            $_SESSION['success'] = "تم حذف المشترك بنجاح";
        } else {
            $_SESSION['error'] = "فشل في حذف المشترك";
        }
    } catch (Exception $e) {
        $_SESSION['error'] = "حدث خطأ: " . $e->getMessage();
    }
}

header("Location: add_simple_agent.php");
exit();
