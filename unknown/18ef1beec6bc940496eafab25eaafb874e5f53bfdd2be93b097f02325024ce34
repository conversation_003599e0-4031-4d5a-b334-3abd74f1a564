<?php
class CoverageMap {
    private $conn;

    public function __construct($conn) {
        $this->conn = $conn;
    }

    /**
     * حساب المسافة بين نقطتين باستخدام صيغة هافرساين
     * @param float $lat1 خط العرض للنقطة الأولى
     * @param float $lon1 خط الطول للنقطة الأولى
     * @param float $lat2 خط العرض للنقطة الثانية
     * @param float $lon2 خط الطول للنقطة الثانية
     * @return float المسافة بالكيلومترات
     */
    public function calculateDistance($lat1, $lon1, $lat2, $lon2) {
        // تحويل الإحداثيات إلى راديان
        $lat1 = deg2rad($lat1);
        $lon1 = deg2rad($lon1);
        $lat2 = deg2rad($lat2);
        $lon2 = deg2rad($lon2);

        // صيغة هافرساين
        $dlat = $lat2 - $lat1;
        $dlon = $lon2 - $lon1;
        $a = sin($dlat/2) * sin($dlat/2) + cos($lat1) * cos($lat2) * sin($dlon/2) * sin($dlon/2);
        $c = 2 * atan2(sqrt($a), sqrt(1-$a));
        
        // نصف قطر الأرض بالكيلومترات
        $r = 6371;
        
        return $r * $c;
    }

    /**
     * الحصول على معلومات التغطية للريبيتر
     * @param int $repeater_id معرف الريبيتر
     * @return array معلومات التغطية
     */
    public function getRepeaterCoverage($repeater_id) {
        try {
            // جلب معلومات الريبيتر
            $stmt = $this->conn->prepare("
                SELECT r.*, t.name as tower_name, t.latitude as tower_lat, t.longitude as tower_lon 
                FROM repeaters r
                LEFT JOIN towers t ON r.tower_id = t.id
                WHERE r.id = ?
            ");
            
            if (!$stmt) {
                throw new Exception("خطأ في إعداد الاستعلام: " . $this->conn->error);
            }

            $stmt->bind_param("i", $repeater_id);
            
            if (!$stmt->execute()) {
                throw new Exception("خطأ في تنفيذ الاستعلام: " . $stmt->error);
            }

            $result = $stmt->get_result();
            $repeater = $result->fetch_assoc();
            
            if (!$repeater) {
                throw new Exception("لم يتم العثور على الريبيتر");
            }

            // جلب المشتركين المرتبطين بهذا الريبيتر
            $agents = $this->conn->query("
                SELECT a.*, r.name as repeater_name 
                FROM agents a
                LEFT JOIN repeaters r ON a.repeater_id = r.id
                WHERE a.repeater_id = $repeater_id
            ")->fetch_all(MYSQLI_ASSOC);

            // حساب المسافات
            $coverage_data = [
                'repeater' => $repeater,
                'tower_distance' => $this->calculateDistance(
                    $repeater['latitude'], 
                    $repeater['longitude'],
                    $repeater['tower_lat'],
                    $repeater['tower_lon']
                ),
                'agents' => []
            ];

            foreach ($agents as $agent) {
                $distance_to_repeater = $this->calculateDistance(
                    $agent['latitude'],
                    $agent['longitude'],
                    $repeater['latitude'],
                    $repeater['longitude']
                );

                $coverage_data['agents'][] = [
                    'agent' => $agent,
                    'distance' => $distance_to_repeater,
                    'within_coverage' => $distance_to_repeater <= $repeater['coverage_radius']
                ];
            }

            return ['success' => true, 'data' => $coverage_data];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * الحصول على معلومات التغطية للبرج
     * @param int $tower_id معرف البرج
     * @return array معلومات التغطية
     */
    public function getTowerCoverage($tower_id) {
        try {
            // جلب معلومات البرج
            $stmt = $this->conn->prepare("SELECT * FROM towers WHERE id = ?");
            if (!$stmt) {
                throw new Exception("خطأ في إعداد الاستعلام: " . $this->conn->error);
            }

            $stmt->bind_param("i", $tower_id);
            
            if (!$stmt->execute()) {
                throw new Exception("خطأ في تنفيذ الاستعلام: " . $stmt->error);
            }

            $result = $stmt->get_result();
            $tower = $result->fetch_assoc();
            
            if (!$tower) {
                throw new Exception("لم يتم العثور على البرج");
            }

            // جلب الريبيترات المرتبطة بهذا البرج
            $repeaters = $this->conn->query("
                SELECT r.*, COUNT(a.id) as agents_count 
                FROM repeaters r
                LEFT JOIN agents a ON a.repeater_id = r.id
                WHERE r.tower_id = $tower_id
                GROUP BY r.id
            ")->fetch_all(MYSQLI_ASSOC);

            // حساب معلومات التغطية لكل ريبيتر
            $coverage_data = [
                'tower' => $tower,
                'repeaters' => []
            ];

            foreach ($repeaters as $repeater) {
                $distance = $this->calculateDistance(
                    $repeater['latitude'],
                    $repeater['longitude'],
                    $tower['latitude'],
                    $tower['longitude']
                );

                $coverage_data['repeaters'][] = [
                    'repeater' => $repeater,
                    'distance' => $distance,
                    'agents_count' => $repeater['agents_count']
                ];
            }

            return ['success' => true, 'data' => $coverage_data];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
}
