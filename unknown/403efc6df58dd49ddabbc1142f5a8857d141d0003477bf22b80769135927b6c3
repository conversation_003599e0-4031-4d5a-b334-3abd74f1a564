<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once '../includes/init.php';

// التحقق من الصلاحيات
check_login();
if (!check_permission('can_delete_agents')) {
    $_SESSION['error'] = "ليس لديك صلاحية لحذف المشتركين";
    header('Location: agents.php');
    exit();
}

// السماح بالوصول من أي مصدر (CORS)
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// إذا كان الطلب هو OPTIONS (preflight request)، نقوم بإنهاء الطلب هنا
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

try {
    // التحقق من وجود معرف المشترك
    if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
        throw new Exception('معرف المشترك غير صحيح');
    }

    $agent_id = intval($_GET['id']);

    // التحقق من وجود المشترك
    $check_sql = "SELECT id FROM agents WHERE id = ?";
    $check_stmt = $conn->prepare($check_sql);
    $check_stmt->bind_param('i', $agent_id);
    $check_stmt->execute();
    $check_result = $check_stmt->get_result();
    
    if ($check_result->num_rows === 0) {
        throw new Exception('المشترك غير موجود');
    }

    // بدء المعاملة
    $conn->begin_transaction();

    try {
        // حذف عناوين IP المرتبطة بالمشترك أولاً
        $stmt = $conn->prepare("DELETE FROM agent_ips WHERE agent_id = ?");
        $stmt->bind_param('i', $agent_id);
        $stmt->execute();

        // حذف المشترك
        $stmt = $conn->prepare("DELETE FROM agents WHERE id = ?");
        $stmt->bind_param('i', $agent_id);
        $stmt->execute();

        // تأكيد المعاملة
        $conn->commit();
        
        // إضافة رسالة نجاح
        $_SESSION['success'] = "تم حذف المشترك بنجاح";
        
        // إرجاع استجابة JSON للطلبات المرسلة بواسطة fetch
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode(['success' => true, 'message' => 'تم حذف المشترك بنجاح']);
        exit();
    } catch (Exception $e) {
        // التراجع عن المعاملة في حالة الخطأ
        $conn->rollback();
        throw $e;
    }
} catch (Exception $e) {
    // التراجع عن المعاملة في حالة الخطأ
    if ($conn->inTransaction()) {
        $conn->rollback();
    }
    
    // إرجاع رسالة خطأ بتنسيق JSON للطلبات المرسلة بواسطة fetch
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    
    // تسجيل رسالة الخطأ في الجلسة للاستخدام اللاحق
    $_SESSION['error'] = "حدث خطأ أثناء حذف المشترك: " . $e->getMessage();
} finally {
    // إغلاق الاتصال
    if (isset($conn) && !$conn->connect_error) {
        $conn->close();
    }
}
