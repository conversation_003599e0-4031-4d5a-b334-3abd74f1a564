<?php
class CoverageStats {
    private $conn;
    
    public function __construct($conn) {
        $this->conn = $conn;
    }
    
    /**
     * الحصول على إحصائيات عامة للتغطية
     */
    public function getGeneralStats() {
        try {
            // إجمالي عدد الريبيترات
            $repeaters_count = $this->conn->query("SELECT COUNT(*) as count FROM repeaters")->fetch_assoc()['count'];
            
            // إجمالي عدد المشتركين
            $agents_count = $this->conn->query("SELECT COUNT(*) as count FROM agents")->fetch_assoc()['count'];
            
            // عدد المشتركين المتصلين بريبيترات
            $connected_agents = $this->conn->query("
                SELECT COUNT(*) as count 
                FROM agents 
                WHERE repeater_id IS NOT NULL
            ")->fetch_assoc()['count'];
            
            // متوسط عدد المشتركين لكل ريبيتر
            $avg_agents = $this->conn->query("
                SELECT AVG(agent_count) as avg 
                FROM (
                    SELECT repeater_id, COUNT(*) as agent_count 
                    FROM agents 
                    WHERE repeater_id IS NOT NULL 
                    GROUP BY repeater_id
                ) t
            ")->fetch_assoc()['avg'];
            
            return [
                'success' => true,
                'data' => [
                    'repeaters_count' => $repeaters_count,
                    'agents_count' => $agents_count,
                    'connected_agents' => $connected_agents,
                    'unconnected_agents' => $agents_count - $connected_agents,
                    'avg_agents_per_repeater' => round($avg_agents, 2)
                ]
            ];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * تحليل جودة التغطية
     */
    public function analyzeCoverageQuality() {
        try {
            // المشتركين خارج نطاق التغطية
            $uncovered_agents = $this->conn->query("
                SELECT 
                    a.*,
                    r.name as repeater_name,
                    r.coverage_radius,
                    r.latitude as repeater_latitude,
                    r.longitude as repeater_longitude
                FROM agents a
                LEFT JOIN repeaters r ON a.repeater_id = r.id
                WHERE a.repeater_id IS NULL
                   OR ST_Distance_Sphere(
                        POINT(a.longitude, a.latitude),
                        POINT(r.longitude, r.latitude)
                    ) > r.coverage_radius * 1000
            ")->fetch_all(MYSQLI_ASSOC);
            
            // الريبيترات بدون وكلاء
            $unused_repeaters = $this->conn->query("
                SELECT r.*
                FROM repeaters r
                WHERE NOT EXISTS (
                    SELECT 1 FROM agents a
                    WHERE a.repeater_id = r.id
                )
            ")->fetch_all(MYSQLI_ASSOC);
            
            // تداخل التغطية بين الريبيترات
            $overlapping_repeaters = $this->conn->query("
                SELECT 
                    r1.id as repeater1_id,
                    r1.name as repeater1_name,
                    r2.id as repeater2_id,
                    r2.name as repeater2_name,
                    ST_Distance_Sphere(
                        POINT(r1.longitude, r1.latitude),
                        POINT(r2.longitude, r2.latitude)
                    ) / 1000 as distance
                FROM repeaters r1
                JOIN repeaters r2 ON r1.id < r2.id
                WHERE ST_Distance_Sphere(
                    POINT(r1.longitude, r1.latitude),
                    POINT(r2.longitude, r2.latitude)
                ) <= GREATEST(r1.coverage_radius, r2.coverage_radius) * 1000
            ")->fetch_all(MYSQLI_ASSOC);
            
            return [
                'success' => true,
                'data' => [
                    'uncovered_agents' => $uncovered_agents,
                    'uncovered_agents_count' => count($uncovered_agents),
                    'unused_repeaters' => $unused_repeaters,
                    'unused_repeaters_count' => count($unused_repeaters),
                    'overlapping_repeaters' => $overlapping_repeaters,
                    'overlapping_repeaters_count' => count($overlapping_repeaters)
                ]
            ];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
    
    /**
     * الحصول على تنبيهات التغطية
     */
    public function getCoverageAlerts() {
        try {
            $alerts = [];
            
            // تنبيه للوكلاء خارج نطاق التغطية
            $uncovered_agents = $this->conn->query("
                SELECT COUNT(*) as count
                FROM agents a
                LEFT JOIN repeaters r ON a.repeater_id = r.id
                WHERE a.repeater_id IS NULL
                   OR ST_Distance_Sphere(
                        POINT(a.longitude, a.latitude),
                        POINT(r.longitude, r.latitude)
                    ) > r.coverage_radius * 1000
            ")->fetch_assoc()['count'];
            
            if ($uncovered_agents > 0) {
                $alerts[] = [
                    'type' => 'danger',
                    'message' => "يوجد {$uncovered_agents} وكيل خارج نطاق التغطية أو غير متصل بريبيتر"
                ];
            }
            
            // تنبيه للريبيترات بدون وكلاء
            $unused_repeaters = $this->conn->query("
                SELECT COUNT(*) as count
                FROM repeaters r
                WHERE NOT EXISTS (
                    SELECT 1 FROM agents a
                    WHERE a.repeater_id = r.id
                )
            ")->fetch_assoc()['count'];
            
            if ($unused_repeaters > 0) {
                $alerts[] = [
                    'type' => 'warning',
                    'message' => "يوجد {$unused_repeaters} ريبيتر بدون وكلاء متصلين"
                ];
            }
            
            // تنبيه للريبيترات المتداخلة
            $overlapping_repeaters = $this->conn->query("
                SELECT COUNT(*) as count
                FROM (
                    SELECT r1.id
                    FROM repeaters r1
                    JOIN repeaters r2 ON r1.id < r2.id
                    WHERE ST_Distance_Sphere(
                        POINT(r1.longitude, r1.latitude),
                        POINT(r2.longitude, r2.latitude)
                    ) <= GREATEST(r1.coverage_radius, r2.coverage_radius) * 1000
                ) t
            ")->fetch_assoc()['count'];
            
            if ($overlapping_repeaters > 0) {
                $alerts[] = [
                    'type' => 'info',
                    'message' => "يوجد {$overlapping_repeaters} تداخل في تغطية الريبيترات"
                ];
            }
            
            return [
                'success' => true,
                'data' => $alerts
            ];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
}
