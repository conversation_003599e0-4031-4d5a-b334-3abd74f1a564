<?php
require_once '../includes/init.php';
$page_title = "لوحة التحكم";
include '../includes/header.php';
require_once '../includes/backup.php';

// دالة مساعدة للتعامل مع الاستعلامات
function executeQuerySafely($conn, $query) {
    $result = $conn->query($query);
    if ($result === false) {
        error_log("SQL Error: " . $conn->error . " in query: " . $query);
        return null;
    }
    return $result;
}

// دالة آمنة لتنسيق الأرقام
function safe_number_format($number, $decimals = 0) {
    return number_format((float)($number ?? 0), $decimals);
}

// جلب إحصائيات المشتركين
$agents_query = executeQuerySafely($conn, "
    SELECT 
        COUNT(*) as total,
        SUM(CASE WHEN unms_status = 'active' THEN 1 ELSE 0 END) as active
    FROM agents
");
$agents_stats = $agents_query ? $agents_query->fetch_assoc() : ['total' => 0, 'active' => 0];

// جلب إحصائيات أنواع الخدمة
$service_types_query = executeQuerySafely($conn, "SELECT COUNT(*) as total FROM service_types WHERE status = 'active'");
$service_types_count = $service_types_query ? $service_types_query->fetch_assoc()['total'] : 0;

// جلب إحصائيات مواقع الأبراج
$tower_locations_query = executeQuerySafely($conn, "SELECT COUNT(*) as total FROM tower_locations WHERE status = 'active'");
$tower_locations_count = $tower_locations_query ? $tower_locations_query->fetch_assoc()['total'] : 0;

// جلب إحصائيات أسماء الفروع
$branch_names_query = executeQuerySafely($conn, "SELECT COUNT(*) as total FROM branch_names WHERE status = 'active'");
$branch_names_count = $branch_names_query ? $branch_names_query->fetch_assoc()['total'] : 0;



// جلب آخر النسخ الاحتياطية
$backup = new DatabaseBackup($conn);
$recent_backups = array_slice($backup->getBackupsList(), 0, 3);
$backup_count = count($backup->getBackupsList());

// جلب إحصائيات التذاكر (إذا كان الجدول موجود)
$tickets_stats = ['total' => 0, 'new' => 0, 'in_progress' => 0, 'resolved' => 0];
$table_check = $conn->query("SHOW TABLES LIKE 'tickets'");
if ($table_check && $table_check->num_rows > 0) {
    $tickets_query = executeQuerySafely($conn, "
        SELECT
            COUNT(*) as total,
            SUM(CASE WHEN status = 'new' THEN 1 ELSE 0 END) as new,
            SUM(CASE WHEN status = 'in_progress' THEN 1 ELSE 0 END) as in_progress,
            SUM(CASE WHEN status = 'resolved' THEN 1 ELSE 0 END) as resolved,
            SUM(CASE WHEN status = 'closed' THEN 1 ELSE 0 END) as closed
        FROM tickets
    ");
    if ($tickets_query) {
        $raw_stats = $tickets_query->fetch_assoc();
        // التأكد من أن القيم ليست null
        $tickets_stats = [
            'total' => (int)($raw_stats['total'] ?? 0),
            'new' => (int)($raw_stats['new'] ?? 0),
            'in_progress' => (int)($raw_stats['in_progress'] ?? 0),
            'resolved' => (int)($raw_stats['resolved'] ?? 0),
            'closed' => (int)($raw_stats['closed'] ?? 0)
        ];
    }
}

// جلب آخر الأنشطة
$activities_query = executeQuerySafely($conn, "
    SELECT * FROM activity_log
    ORDER BY created_at DESC
    LIMIT 5
");
$activities = $activities_query ? $activities_query : [];

// جلب آخر المشتركين المضافين
$latest_agents_query = executeQuerySafely($conn, "
    SELECT a.*
    FROM agents a
    ORDER BY a.created_at DESC
    LIMIT 5
");
$latest_agents = $latest_agents_query ? $latest_agents_query : [];
?>

<style>
/* تأثيرات إضافية للوحة التحكم */
.card {
    transition: all 0.3s ease !important;
}

.card:hover {
    transform: translateY(-10px) scale(1.02) !important;
    box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
}

.icon-circle {
    transition: all 0.3s ease;
}

.card:hover .icon-circle {
    transform: rotate(360deg) scale(1.1);
}

.animate__fadeInUp {
    animation-duration: 0.8s;
}

/* خلفية ثابتة بالألوان الجديدة */
body {
    background: linear-gradient(135deg, #38C5E6 0%, #0B8582 50%, #01233D 100%);
    background-attachment: fixed;
    min-height: 100vh;
}

/* تحسين الشفافية */
.container-fluid {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    margin: 1rem;
}

/* تأثير النبض للأيقونات - مبسط */
.icon-circle i {
    transition: transform 0.3s ease;
}

.icon-circle:hover i {
    transform: scale(1.1);
}

/* تحسين النصوص */
h3 {
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* تأثير التمرير السلس */
html {
    scroll-behavior: smooth;
}

/* تحسين الظلال */
.shadow-lg {
    box-shadow: 0 15px 35px rgba(0,0,0,0.1), 0 5px 15px rgba(0,0,0,0.07) !important;
}

/* تحسين الجداول */
.table tbody tr:hover {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%) !important;
    transform: translateX(5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.table tbody tr:hover a {
    color: #667eea !important;
}

/* تحسين الشارات */
.badge {
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
    transition: all 0.3s ease;
}

.badge:hover {
    transform: scale(1.05);
}

/* تأثير التمرير للروابط */
a {
    transition: all 0.3s ease;
}

a:hover {
    transform: translateY(-1px);
}

/* تحسين الأيقونات */
.fas, .fab {
    transition: all 0.3s ease;
}

.card:hover .fas {
    transform: scale(1.1);
}

/* تأثير بسيط للكاردات */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}
</style>

<div class="container-fluid py-4">
    <!-- رأس الصفحة المحدث -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-lg" style="background: linear-gradient(135deg, #01233D 0%, #01233D 100%); border-radius: 20px;">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="text-white">
                            <h1 class="h2 mb-2 fw-bold">
                                <i class="fas fa-tachometer-alt me-3" style="font-size: 2.5rem;"></i>
                                لوحة التحكم الرئيسية
                            </h1>
                            <p class="mb-0 opacity-75" style="font-size: 1.1rem;">
                                <i class="fas fa-calendar-alt me-2"></i>
                                مرحباً بك، <?php echo htmlspecialchars($_SESSION['user_name']); ?> -
                                <?php echo date('l, F j, Y'); ?>
                            </p>
                        </div>
                        <div class="d-flex flex-column gap-2">
                            <?php if (check_permission('can_add_agents')): ?>
                            <a href="<?php echo get_path('/pages/add_agent.php'); ?>"
                               class="btn btn-light btn-lg shadow-sm"
                               style="border-radius: 15px; font-weight: 600;">
                                <i class="fas fa-user-plus me-2"></i> إضافة مشترك جديد
                            </a>
                            <?php endif; ?>

                            <?php if (check_permission('can_manage_backups')): ?>
                            <a href="<?php echo get_path('/pages/backup.php'); ?>"
                               class="btn btn-outline-light btn-lg shadow-sm"
                               style="border-radius: 15px; font-weight: 600;">
                                <i class="fas fa-cloud-arrow-down me-2"></i> النسخ الاحتياطي
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row">


        <!-- النسخ الاحتياطية -->
        <div class="col-xl-3 col-md-6 mb-4">
            <a href="<?php echo get_path('/pages/backup.php'); ?>" class="text-decoration-none">
                <div class="card h-100 border-0 shadow-lg animate__animated animate__fadeInUp"
                     style="animation-delay: 0.1s; border-radius: 20px; background: linear-gradient(135deg, #38C5E6 0%, #0B8582 100%); transition: all 0.3s ease;">
                    <div class="card-body p-4 text-white position-relative overflow-hidden">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <div class="d-flex align-items-center mb-2">
                                    <div class="icon-circle me-3" style="width: 60px; height: 60px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-database" style="font-size: 1.8rem;"></i>
                                    </div>
                                    <div>
                                        <p class="mb-1 opacity-75" style="font-size: 0.9rem;">النسخ الاحتياطية</p>
                                        <h3 class="mb-0 fw-bold"><?php echo number_format($backup_count); ?></h3>
                                    </div>
                                </div>
                                <p class="mb-0 opacity-75" style="font-size: 0.85rem;">
                                    <i class="fas fa-check-circle me-1"></i>
                                    متوفرة ومحدثة
                                </p>
                            </div>
                        </div>
                        <div class="position-absolute" style="top: -20px; right: -20px; opacity: 0.1;">
                            <i class="fas fa-database" style="font-size: 5rem;"></i>
                        </div>
                    </div>
                </div>
            </a>
        </div>

        <!-- التذاكر -->
        <div class="col-xl-3 col-md-6 mb-4">
            <a href="<?php echo get_path('/pages/tickets.php'); ?>" class="text-decoration-none">
                <div class="card h-100 border-0 shadow-lg animate__animated animate__fadeInUp"
                     style="animation-delay: 0.2s; border-radius: 20px; background: linear-gradient(135deg, #0B8582 0%, #01233D 100%); transition: all 0.3s ease;">
                    <div class="card-body p-4 text-white position-relative overflow-hidden">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <div class="d-flex align-items-center mb-2">
                                    <div class="icon-circle me-3" style="width: 60px; height: 60px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-ticket-alt" style="font-size: 1.8rem;"></i>
                                    </div>
                                    <div>
                                        <p class="mb-1 opacity-75" style="font-size: 0.9rem;">التذاكر</p>
                                        <h3 class="mb-0 fw-bold"><?php echo number_format($tickets_stats['total']); ?></h3>
                                    </div>
                                </div>
                                <p class="mb-0 opacity-75" style="font-size: 0.85rem;">
                                    <i class="fas fa-exclamation-circle me-1"></i>
                                    <?php echo number_format($tickets_stats['new'] + $tickets_stats['in_progress']); ?> مفتوحة
                                </p>
                            </div>
                        </div>
                        <div class="position-absolute" style="top: -20px; right: -20px; opacity: 0.1;">
                            <i class="fas fa-ticket-alt" style="font-size: 5rem;"></i>
                        </div>
                    </div>
                </div>
            </a>
        </div>







        <!-- نوع الخدمة -->
        <div class="col-xl-3 col-md-6 mb-4">
            <a href="<?php echo get_path('/pages/service_types.php'); ?>" class="text-decoration-none">
                <div class="card h-100 border-0 shadow-lg animate__animated animate__fadeInUp"
                     style="animation-delay: 0.3s; border-radius: 20px; background: linear-gradient(135deg, #38C5E6 0%, #01233D 100%); transition: all 0.3s ease;">
                    <div class="card-body p-4 text-white position-relative overflow-hidden">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <div class="d-flex align-items-center mb-2">
                                    <div class="icon-circle me-3" style="width: 60px; height: 60px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-concierge-bell" style="font-size: 1.8rem;"></i>
                                    </div>
                                    <div>
                                        <p class="mb-1 opacity-75" style="font-size: 0.9rem;">أنواع الخدمة</p>
                                        <h3 class="mb-0 fw-bold"><?php echo number_format($service_types_count); ?></h3>
                                    </div>
                                </div>
                                <p class="mb-0 opacity-75" style="font-size: 0.85rem;">
                                    <i class="fas fa-check-circle me-1"></i>
                                    نشطة ومتاحة
                                </p>
                            </div>
                        </div>
                        <div class="position-absolute" style="top: -20px; right: -20px; opacity: 0.1;">
                            <i class="fas fa-concierge-bell" style="font-size: 5rem;"></i>
                        </div>
                    </div>
                </div>
            </a>
        </div>

        <!-- مكان البرج -->
        <div class="col-xl-3 col-md-6 mb-4">
            <a href="<?php echo get_path('/pages/tower_locations.php'); ?>" class="text-decoration-none">
                <div class="card h-100 border-0 shadow-lg animate__animated animate__fadeInUp"
                     style="animation-delay: 0.4s; border-radius: 20px; background: linear-gradient(135deg, #0B8582 0%, #38C5E6 100%); transition: all 0.3s ease;">
                    <div class="card-body p-4 text-white position-relative overflow-hidden">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <div class="d-flex align-items-center mb-2">
                                    <div class="icon-circle me-3" style="width: 60px; height: 60px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-tower-broadcast" style="font-size: 1.8rem;"></i>
                                    </div>
                                    <div>
                                        <p class="mb-1 opacity-75" style="font-size: 0.9rem;">مواقع الأبراج</p>
                                        <h3 class="mb-0 fw-bold"><?php echo number_format($tower_locations_count); ?></h3>
                                    </div>
                                </div>
                                <p class="mb-0 opacity-75" style="font-size: 0.85rem;">
                                    <i class="fas fa-signal me-1"></i>
                                    متاحة للخدمة
                                </p>
                            </div>
                        </div>
                        <div class="position-absolute" style="top: -20px; right: -20px; opacity: 0.1;">
                            <i class="fas fa-tower-broadcast" style="font-size: 5rem;"></i>
                        </div>
                    </div>
                </div>
            </a>
        </div>

        <!-- اسم الفرع -->
        <div class="col-xl-3 col-md-6 mb-4">
            <a href="<?php echo get_path('/pages/branch_names.php'); ?>" class="text-decoration-none">
                <div class="card h-100 border-0 shadow-lg animate__animated animate__fadeInUp"
                     style="animation-delay: 0.5s; border-radius: 20px; background: linear-gradient(135deg, #01233D 0%, #38C5E6 100%); transition: all 0.3s ease;">
                    <div class="card-body p-4 text-white position-relative overflow-hidden">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <div class="d-flex align-items-center mb-2">
                                    <div class="icon-circle me-3" style="width: 60px; height: 60px; background: rgba(255,255,255,0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                        <i class="fas fa-building" style="font-size: 1.8rem;"></i>
                                    </div>
                                    <div>
                                        <p class="mb-1 opacity-75" style="font-size: 0.9rem;">أسماء الفروع</p>
                                        <h3 class="mb-0 fw-bold"><?php echo number_format($branch_names_count); ?></h3>
                                    </div>
                                </div>
                                <p class="mb-0 opacity-75" style="font-size: 0.85rem;">
                                    <i class="fas fa-map-marker-alt me-1"></i>
                                    مسجلة ونشطة
                                </p>
                            </div>
                        </div>
                        <div class="position-absolute" style="top: -20px; right: -20px; opacity: 0.1;">
                            <i class="fas fa-building" style="font-size: 5rem;"></i>
                        </div>
                    </div>
                </div>
            </a>
        </div>
    </div>

    <!-- المعلومات التفصيلية -->
    <div class="row">
        <!-- آخر المشتركين المضافين -->
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-lg animate__animated animate__fadeInLeft"
                 style="animation-delay: 0.6s; border-radius: 20px; background: rgba(255,255,255,0.95); backdrop-filter: blur(10px);">
                <div class="card-header border-0 pb-0"
                     style="background: linear-gradient(135deg, #01233D 0%, #0B8582 100%); border-radius: 20px 20px 0 0;">
                    <div class="d-flex align-items-center justify-content-between p-3">
                        <h5 class="mb-0 text-white fw-bold">
                            <i class="fas fa-users me-3" style="font-size: 1.5rem;"></i>
                            آخر المشتركين المضافين
                        </h5>
                        <div class="text-white opacity-75">
                            <i class="fas fa-clock me-1"></i>
                            محدث الآن
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" style="border-radius: 15px; overflow: hidden;">
                            <thead style="background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);">
                                <tr>
                                    <th class="border-0 fw-bold text-dark" style="padding: 1rem;">
                                        <i class="fas fa-user me-2 text-primary"></i>المشترك
                                    </th>
                                    <th class="border-0 fw-bold text-dark" style="padding: 1rem;">
                                        <i class="fas fa-map-marker-alt me-2 text-success"></i>نقطة الاتصال
                                    </th>
                                    <th class="border-0 fw-bold text-dark text-center" style="padding: 1rem;">
                                        <i class="fas fa-toggle-on me-2 text-warning"></i>الحالة
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if($latest_agents && $latest_agents->num_rows > 0): while($agent = $latest_agents->fetch_assoc()): ?>
                                <tr class="border-0" style="transition: all 0.3s ease;">
                                    <td class="border-0" style="padding: 1rem;">
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                <div class="rounded-circle d-flex align-items-center justify-content-center"
                                                     style="width: 50px; height: 50px; background: linear-gradient(135deg, #38C5E6 0%, #0B8582 100%); box-shadow: 0 4px 15px rgba(56, 197, 230, 0.3);">
                                                    <i class="fas fa-user text-white" style="font-size: 1.2rem;"></i>
                                                </div>
                                            </div>
                                            <div>
                                                <h6 class="mb-1 fw-bold">
                                                    <a href="agent_details.php?id=<?php echo $agent['id']; ?>"
                                                       class="text-decoration-none text-dark"
                                                       style="transition: color 0.3s ease;">
                                                        <?php echo htmlspecialchars($agent['agent_name']); ?>
                                                    </a>
                                                </h6>
                                                <p class="mb-0 text-muted" style="font-size: 0.9rem;">
                                                    <i class="fas fa-phone me-1"></i>
                                                    <?php echo htmlspecialchars($agent['phone_number']); ?>
                                                </p>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="border-0" style="padding: 1rem;">
                                        <div class="d-flex align-items-center">
                                            <div class="rounded-circle bg-success bg-opacity-10 p-2 me-2">
                                                <i class="fas fa-map-marker-alt text-success"></i>
                                            </div>
                                            <span class="fw-semibold"><?php echo htmlspecialchars($agent['point_name']); ?></span>
                                        </div>
                                    </td>
                                    <td class="border-0 text-center" style="padding: 1rem;">
                                        <?php if($agent['unms_status'] == 'active'): ?>
                                            <span class="badge rounded-pill px-3 py-2"
                                                  style="background: linear-gradient(135deg, #38C5E6 0%, #0B8582 100%); color: white; font-size: 0.85rem;">
                                                <i class="fas fa-check-circle me-1"></i>نشط
                                            </span>
                                        <?php else: ?>
                                            <span class="badge rounded-pill px-3 py-2"
                                                  style="background: linear-gradient(135deg, #01233D 0%, #0B8582 100%); color: white; font-size: 0.85rem;">
                                                <i class="fas fa-pause-circle me-1"></i>غير نشط
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endwhile; else: ?>
                                <tr>
                                    <td colspan="5" class="text-center">لا يوجد وكلاء حالياً</td>
                                </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer bg-light text-center">
                    <a href="<?php echo get_path('/pages/agents.php'); ?>" class="btn btn-sm btn-primary">
                        <i class="bi bi-list me-1"></i> عرض جميع المشتركين
                    </a>
                </div>
            </div>
        </div>

        <!-- النسخ الاحتياطية وسجل النشاطات -->
        <div class="col-lg-6">
            <div class="card h-100 animate__animated animate__fadeIn" style="animation-delay: 0.6s">
                <div class="card-header bg-light">
                    <h5 class="mb-0">
                        <i class="fa-solid fa-chart-line me-2 text-primary"></i>
                        آخر الأنشطة والنسخ الاحتياطية
                    </h5>
                </div>
                <div class="card-body p-3">
                    <div class="timeline">
                        <?php if($activities && $activities->num_rows > 0): while($activity = $activities->fetch_assoc()): ?>
                        <div class="timeline-item animate__animated animate__fadeInRight" style="animation-delay: 0.7s">
                            <div class="timeline-item-content">
                                <span class="tag" style="background-color: #e17055">
                                    <?php echo isset($activity['type']) ? htmlspecialchars($activity['type']) : 'نشاط'; ?>
                                </span>
                                <time><?php echo isset($activity['created_at']) ? date('Y-m-d H:i', strtotime($activity['created_at'])) : ''; ?></time>
                                <p><?php echo isset($activity['description']) ? htmlspecialchars($activity['description']) : ''; ?></p>
                            </div>
                        </div>
                        <?php endwhile; endif; ?>

                        <?php foreach($recent_backups as $index => $backup): ?>
                        <div class="timeline-item animate__animated animate__fadeInRight" style="animation-delay: <?php echo 0.8 + ($index * 0.1); ?>s">
                            <div class="timeline-item-content">
                                <span class="tag" style="background-color: #00b894">نسخة احتياطية</span>
                                <time><?php echo isset($backup['created_at']) ? date('Y-m-d H:i', strtotime($backup['created_at'])) : date('Y-m-d H:i'); ?></time>
                                <p>تم إنشاء نسخة احتياطية جديدة</p>
                                <?php if(isset($backup['path'])): ?>
                                <a href="<?php echo htmlspecialchars($backup['path']); ?>" class="btn btn-sm btn-outline-primary">
                                    <i class="bi bi-download"></i> تحميل
                                </a>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <div class="card-footer bg-light text-center">
                    <a href="<?php echo get_path('/pages/activity_log.php'); ?>" class="btn btn-sm btn-info me-2">
                        <i class="bi bi-list-check me-1"></i> سجل النشاطات
                    </a>
                    <a href="<?php echo get_path('/pages/backup.php'); ?>" class="btn btn-sm btn-success">
                        <i class="bi bi-cloud-download me-1"></i> النسخ الاحتياطية
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- قسم إحصائيات التذاكر (للمديرين فقط) -->
    <?php if ($_SESSION['user_role'] === 'admin' && $tickets_stats['total'] > 0): ?>
    <div class="row mt-4">
        <div class="col-12">
            <div class="card animate__animated animate__fadeIn" style="animation-delay: 0.7s">
                <div class="card-header bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0">
                            <i class="fa-solid fa-ticket me-2 text-primary"></i>
                            إحصائيات التذاكر
                        </h5>
                        <a href="<?php echo get_path('/pages/tickets/index.php'); ?>" class="btn btn-sm btn-primary">
                            <i class="fa-solid fa-eye me-1"></i> عرض جميع التذاكر
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- إجمالي التذاكر -->
                        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                            <div class="text-center p-3 border rounded">
                                <div class="icon-shape bg-gradient-primary shadow-primary text-center rounded-circle mx-auto mb-2" style="width: 50px; height: 50px;">
                                    <i class="fa-solid fa-ticket text-lg opacity-10"></i>
                                </div>
                                <h4 class="fw-bold mb-0"><?php echo safe_number_format($tickets_stats['total']); ?></h4>
                                <p class="text-sm mb-0">إجمالي التذاكر</p>
                            </div>
                        </div>

                        <!-- التذاكر الجديدة -->
                        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                            <div class="text-center p-3 border rounded">
                                <div class="icon-shape bg-gradient-info shadow-info text-center rounded-circle mx-auto mb-2" style="width: 50px; height: 50px;">
                                    <i class="fa-solid fa-plus text-lg opacity-10"></i>
                                </div>
                                <h4 class="fw-bold mb-0 text-info"><?php echo safe_number_format($tickets_stats['new']); ?></h4>
                                <p class="text-sm mb-0">جديد</p>
                            </div>
                        </div>

                        <!-- قيد العمل -->
                        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                            <div class="text-center p-3 border rounded">
                                <div class="icon-shape bg-gradient-warning shadow-warning text-center rounded-circle mx-auto mb-2" style="width: 50px; height: 50px;">
                                    <i class="fa-solid fa-cog text-lg opacity-10"></i>
                                </div>
                                <h4 class="fw-bold mb-0 text-warning"><?php echo safe_number_format($tickets_stats['in_progress']); ?></h4>
                                <p class="text-sm mb-0">قيد العمل</p>
                            </div>
                        </div>

                        <!-- محلول -->
                        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                            <div class="text-center p-3 border rounded">
                                <div class="icon-shape bg-gradient-success shadow-success text-center rounded-circle mx-auto mb-2" style="width: 50px; height: 50px;">
                                    <i class="fa-solid fa-check text-lg opacity-10"></i>
                                </div>
                                <h4 class="fw-bold mb-0 text-success"><?php echo safe_number_format($tickets_stats['resolved']); ?></h4>
                                <p class="text-sm mb-0">محلول</p>
                            </div>
                        </div>

                        <!-- مغلق -->
                        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                            <div class="text-center p-3 border rounded">
                                <div class="icon-shape bg-gradient-secondary shadow-secondary text-center rounded-circle mx-auto mb-2" style="width: 50px; height: 50px;">
                                    <i class="fa-solid fa-lock text-lg opacity-10"></i>
                                </div>
                                <h4 class="fw-bold mb-0 text-secondary"><?php echo safe_number_format($tickets_stats['closed']); ?></h4>
                                <p class="text-sm mb-0">مغلق</p>
                            </div>
                        </div>

                        <!-- إجراءات سريعة -->
                        <div class="col-lg-2 col-md-4 col-sm-6 mb-3">
                            <div class="text-center p-3 border rounded">
                                <a href="<?php echo get_path('/pages/tickets/add.php'); ?>" class="btn btn-primary btn-sm mb-2 w-100">
                                    <i class="fa-solid fa-plus me-1"></i> تذكرة جديدة
                                </a>
                                <a href="<?php echo get_path('/pages/tickets/index.php?status=new'); ?>" class="btn btn-outline-info btn-sm w-100">
                                    <i class="fa-solid fa-eye me-1"></i> التذاكر الجديدة
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<style>
/* تنسيقات لوحة التحكم */
.card {
    box-shadow: 0 0 2rem 0 rgba(136, 152, 170, .15);
    border: 0;
    border-radius: 0.5rem;
    overflow: hidden;
}

.card .card-header {
    padding: 1rem 1.5rem;
    background-color: #f8f9fa;
    border-bottom: 1px solid #eee;
}

.numbers p {
    margin-bottom: 0;
    color: #67748e;
    font-size: 0.875rem;
}

.icon-shape {
    width: 48px;
    height: 48px;
    background-position: center;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.bg-gradient-primary {
    background-image: linear-gradient(310deg, #7928CA 0%, #FF0080 100%);
}

.bg-gradient-success {
    background-image: linear-gradient(310deg, #2DCE89 0%, #2DCE89 100%);
}

.bg-gradient-info {
    background-image: linear-gradient(310deg, #1171ef 0%, #11cdef 100%);
}

.bg-gradient-warning {
    background-image: linear-gradient(310deg, #fb6340 0%, #fbb140 100%);
}

.bg-gradient-danger {
    background-image: linear-gradient(310deg, #ff3737 0%, #ff3737 100%);
}

.dashboard-card {
    box-shadow: 0 0 2rem 0 rgba(136, 152, 170, .15);
    border: 0;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0 2rem 0 rgba(136, 152, 170, .25);
}

.shadow-primary {
    box-shadow: 0 0 2rem 0 rgba(121, 40, 202, .15);
}

.shadow-success {
    box-shadow: 0 0 2rem 0 rgba(45, 206, 137, .15);
}

.shadow-info {
    box-shadow: 0 0 2rem 0 rgba(17, 113, 239, .15);
}

.shadow-warning {
    box-shadow: 0 0 2rem 0 rgba(251, 99, 64, .15);
}

.shadow-danger {
    box-shadow: 0 0 2rem 0 rgba(255, 55, 55, .15);
}

.timeline {
    margin: 0;
    padding: 0;
    list-style: none;
    position: relative;
}

.timeline-item {
    padding-left: 40px;
    position: relative;
    margin-bottom: 1.5rem;
}

.timeline-item::before {
    content: '';
    width: 2px;
    height: 100%;
    background-color: #e9ecef;
    position: absolute;
    left: 7px;
    top: 0;
}

.timeline-item:last-child::before {
    height: 50%;
}

.timeline-item .timeline-item-content {
    position: relative;
    padding: 15px;
    border-radius: 5px;
    background-color: #f8f9fa;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
}

.timeline-item .timeline-item-content::before {
    content: '';
    width: 15px;
    height: 15px;
    background-color: #fff;
    border: 2px solid #e9ecef;
    border-radius: 50%;
    position: absolute;
    left: -34px;
    top: 15px;
}

.timeline-item .timeline-item-content:hover::before {
    background-color: #007bff;
    border-color: #007bff;
}

.timeline-item .timeline-item-content .tag {
    color: #fff;
    font-size: 0.75rem;
    padding: 2px 8px;
    border-radius: 15px;
    display: inline-block;
    margin-bottom: 5px;
}

.timeline-item .timeline-item-content time {
    display: block;
    font-size: 0.75rem;
    color: #6c757d;
    margin-bottom: 8px;
}

.timeline-item .timeline-item-content p {
    margin: 0;
    font-size: 0.875rem;
}

.avatar-circle {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.table-custom {
    margin-bottom: 0;
}

.table-custom th {
    font-weight: 600;
    padding: 12px 16px;
    border-bottom: 1px solid #e9ecef;
}

.table-custom td {
    padding: 12px 16px;
    vertical-align: middle;
    border-bottom: 1px solid #e9ecef;
}

.table-animated tr {
    transition: all 0.3s ease;
}

.table-animated tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

.status-badge {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: 50rem;
}

.status-active {
    background-color: rgba(45, 206, 137, 0.1);
    color: #2dce89;
}

.status-inactive {
    background-color: rgba(130, 134, 139, 0.1);
    color: #82868b;
}

.card-footer {
    padding: 0.75rem 1.5rem;
    background-color: #f8f9fa;
    border-top: 1px solid #eee;
}
</style>

<?php include '../includes/footer.php'; ?>
