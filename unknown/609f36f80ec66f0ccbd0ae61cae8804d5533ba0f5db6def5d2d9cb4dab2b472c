<?php
require_once '../includes/init.php';

// التحقق من صلاحية عرض المشتركين
requirePermission('agents.view_all');

$page_title = "إدارة المشتركين";
include '../includes/header.php';

// إضافة التصميم الجديد
echo "<style>
/* خلفية ثابتة بالألوان الجديدة لصفحة المشتركين */
body {
    background: linear-gradient(135deg, #38C5E6 0%, #0B8582 50%, #01233D 100%);
    background-attachment: fixed;
    min-height: 100vh;
}

.container-fluid {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border-radius: 20px;
    padding: 2rem;
    margin: 1rem;
}

.card {
    transition: all 0.3s ease !important;
    border: none !important;
    border-radius: 20px !important;
    backdrop-filter: blur(10px);
}

.card:hover {
    transform: translateY(-10px) scale(1.02) !important;
    box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
}

.stats-card {
    background: rgba(255,255,255,0.95);
    border-radius: 20px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
}

.stats-card.primary::before { background: linear-gradient(135deg, #38C5E6 0%, #0B8582 100%); }
.stats-card.success::before { background: linear-gradient(135deg, #0B8582 0%, #01233D 100%); }
.stats-card.warning::before { background: linear-gradient(135deg, #38C5E6 0%, #01233D 100%); }

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.15);
}

.table {
    background: rgba(255,255,255,0.95);
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.table thead th {
    background: linear-gradient(135deg, #01233D 0%, #0B8582 100%);
    color: white;
    border: none;
    padding: 1.2rem;
    font-weight: 600;
}

.table tbody tr {
    transition: all 0.3s ease;
    border: none;
}

.table tbody tr:hover {
    background: linear-gradient(135deg, rgba(56, 197, 230, 0.1) 0%, rgba(11, 133, 130, 0.1) 100%);
    transform: translateX(5px);
}

.table tbody td {
    border: none;
    padding: 1rem;
    vertical-align: middle;
}

.btn {
    border-radius: 15px !important;
    padding: 0.75rem 1.5rem !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    border: none !important;
}

.btn:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.btn-primary {
    background: linear-gradient(135deg, #38C5E6 0%, #0B8582 100%) !important;
}

.btn-success {
    background: linear-gradient(135deg, #0B8582 0%, #01233D 100%) !important;
}

.btn-warning {
    background: linear-gradient(135deg, #38C5E6 0%, #01233D 100%) !important;
}

.btn-danger {
    background: linear-gradient(135deg, #01233D 0%, #0B8582 100%) !important;
}

.btn-info {
    background: linear-gradient(135deg, #38C5E6 0%, #0B8582 100%) !important;
}

.form-control, .form-select {
    border-radius: 15px !important;
    border: 2px solid rgba(56, 197, 230, 0.2) !important;
    padding: 0.75rem 1rem !important;
    transition: all 0.3s ease !important;
}

.form-control:focus, .form-select:focus {
    border-color: #38C5E6 !important;
    box-shadow: 0 0 0 0.2rem rgba(56, 197, 230, 0.25) !important;
    transform: translateY(-2px) !important;
}

.badge {
    border-radius: 15px !important;
    padding: 0.5rem 1rem !important;
    font-weight: 600 !important;
}

.animate__fadeInUp {
    animation-duration: 0.8s;
}

/* تحسين وضوح النصوص في قسم البحث المتقدم */
#searchFilters .form-label {
    color: #01233D !important;
    font-weight: 700 !important;
    font-size: 1.1rem !important;
    text-shadow: 1px 1px 2px rgba(255,255,255,0.9) !important;
    background: rgba(255,255,255,0.95) !important;
    padding: 0.4rem 0.8rem !important;
    border-radius: 8px !important;
    display: inline-block !important;
    border: 2px solid rgba(56, 197, 230, 0.3) !important;
    margin-bottom: 0.8rem !important;
}

#searchFilters .card-body {
    background: rgba(255,255,255,0.98) !important;
    color: #01233D !important;
}

/* تحسين وضوح placeholder */
#searchFilters .form-control::placeholder {
    color: #0B8582 !important;
    opacity: 0.8 !important;
    font-weight: 500 !important;
}

/* تحسين وضوح النصوص في select */
#searchFilters .form-select {
    color: #01233D !important;
    font-weight: 600 !important;
}

#searchFilters .form-select option {
    color: #01233D !important;
    background: #ffffff !important;
}
</style>";

// دالة مساعدة للاستعلامات الآمنة
function executeQuery($sql, $params = []) {
    global $conn;
    $stmt = $conn->prepare($sql);
    if ($params) {
        $types = str_repeat('s', count($params));
        $stmt->bind_param($types, ...$params);
    }
    $stmt->execute();
    return $stmt->get_result();
}

// دالة لجلب عدد المشتركين حسب الحالة
function getAgentCount($status = null) {
    $sql = "SELECT COUNT(*) as count FROM agents";
    $params = [];
    if ($status !== null) {
        $sql .= " WHERE unms_status = ?";
        $params[] = $status;
    }
    $result = executeQuery($sql, $params);
    if ($result && $row = $result->fetch_assoc()) {
        return $row['count'];
    }
    return 0;
}

// جلب إحصائيات المشتركين
$total_agents = getAgentCount();
$active_agents = getAgentCount('active');
$inactive_agents = getAgentCount('inactive');


?>

<div class="container-fluid py-4">
    <!-- رأس الصفحة المحدث -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-lg animate__animated animate__fadeInDown"
                 style="background: linear-gradient(135deg, #01233D 0%, #01233D 100%); border-radius: 20px;">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="text-white">
                            <h1 class="h2 mb-2 fw-bold">
                                <i class="fas fa-users me-3" style="font-size: 2.5rem;"></i>
                                إدارة المشتركين
                            </h1>
                            <p class="mb-0 opacity-75" style="font-size: 1.1rem;">
                                <i class="fas fa-chart-line me-2"></i>
                                إدارة شاملة لجميع المشتركين والخدمات
                            </p>
                        </div>
                        <div class="d-flex flex-column gap-2">
                            <?php if (check_permission('can_add_agents')): ?>
                            <a href="<?php echo get_path('/pages/add_agent.php'); ?>"
                               class="btn btn-light btn-lg shadow-sm"
                               style="border-radius: 15px; font-weight: 600;">
                                <i class="fas fa-user-plus me-2"></i> إضافة مشترك جديد
                            </a>
                            <?php endif; ?>

                            <a href="<?php echo get_path('/pages/export.php'); ?>"
                               class="btn btn-outline-light btn-lg shadow-sm"
                               style="border-radius: 15px; font-weight: 600;">
                                <i class="fas fa-file-export me-2"></i> تصدير البيانات
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- بطاقات الإحصائيات المحدثة -->
    <div class="row mb-4">
        <!-- إجمالي المشتركين -->
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="stats-card primary animate__animated animate__fadeInUp" style="animation-delay: 0.1s;">
                <div class="d-flex align-items-center">
                    <div class="rounded-circle d-flex align-items-center justify-content-center me-3"
                         style="width: 70px; height: 70px; background: linear-gradient(135deg, #38C5E6 0%, #0B8582 100%); box-shadow: 0 8px 25px rgba(56, 197, 230, 0.3);">
                        <i class="fas fa-users text-white" style="font-size: 2rem;"></i>
                    </div>
                    <div>
                        <h5 class="fw-bold text-dark mb-1">إجمالي المشتركين</h5>
                        <div class="h2 mb-0 fw-bold" style="color: #38C5E6;"><?php echo number_format($total_agents); ?></div>
                        <small class="text-muted">
                            <i class="fas fa-chart-line me-1"></i>
                            جميع المشتركين المسجلين
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- المشتركين النشطين -->
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="stats-card success animate__animated animate__fadeInUp" style="animation-delay: 0.2s;">
                <div class="d-flex align-items-center">
                    <div class="rounded-circle d-flex align-items-center justify-content-center me-3"
                         style="width: 70px; height: 70px; background: linear-gradient(135deg, #0B8582 0%, #01233D 100%); box-shadow: 0 8px 25px rgba(11, 133, 130, 0.3);">
                        <i class="fas fa-check-circle text-white" style="font-size: 2rem;"></i>
                    </div>
                    <div>
                        <h5 class="fw-bold text-dark mb-1">المشتركين النشطين</h5>
                        <div class="h2 mb-0 fw-bold" style="color: #0B8582;"><?php echo number_format($active_agents); ?></div>
                        <small class="text-muted">
                            <i class="fas fa-signal me-1"></i>
                            متصلين ونشطين حالياً
                        </small>
                    </div>
                </div>
            </div>
        </div>

        <!-- المشتركين غير النشطين -->
        <div class="col-xl-4 col-md-6 mb-4">
            <div class="stats-card warning animate__animated animate__fadeInUp" style="animation-delay: 0.3s;">
                <div class="d-flex align-items-center">
                    <div class="rounded-circle d-flex align-items-center justify-content-center me-3"
                         style="width: 70px; height: 70px; background: linear-gradient(135deg, #38C5E6 0%, #01233D 100%); box-shadow: 0 8px 25px rgba(56, 197, 230, 0.3);">
                        <i class="fas fa-pause-circle text-white" style="font-size: 2rem;"></i>
                    </div>
                    <div>
                        <h5 class="fw-bold text-dark mb-1">المشتركين غير النشطين</h5>
                        <div class="h2 mb-0 fw-bold" style="color: #01233D;"><?php echo number_format($inactive_agents); ?></div>
                        <small class="text-muted">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            غير متصلين حالياً
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث المحدثة -->
    <div class="card mb-4 animate__animated animate__fadeInUp" style="animation-delay: 0.4s; border-radius: 20px; background: rgba(255,255,255,0.95); backdrop-filter: blur(10px);">
        <div class="card-header border-0 pb-0"
             style="background: linear-gradient(135deg, #01233D 0%, #0B8582 100%); border-radius: 20px 20px 0 0;">
            <div class="d-flex align-items-center justify-content-between p-3">
                <h5 class="mb-0 text-white fw-bold">
                    <i class="fas fa-search me-3" style="font-size: 1.5rem;"></i>
                    فلاتر البحث والتصفية
                </h5>
                <div class="text-white opacity-75">
                    <i class="fas fa-filter me-1"></i>
                    بحث متقدم
                </div>
            </div>
        </div>
                <i class="bi bi-funnel-fill me-2"></i>
                البحث المتقدم
                <button class="btn btn-sm btn-link float-end" type="button" data-bs-toggle="collapse" data-bs-target="#searchFilters">
                    <i class="bi bi-chevron-down"></i>
                </button>
            </h5>
        </div>
        <div class="card-body collapse show" id="searchFilters">
            <form id="searchForm" method="GET" class="row g-3">
                <div class="col-md-3">
                    <label class="form-label fw-bold">اسم المشترك</label>
                    <input type="text" name="agent_name" class="form-control" placeholder="ادخل اسم المشترك" value="<?php echo isset($_GET['agent_name']) ? htmlspecialchars($_GET['agent_name']) : ''; ?>">
                </div>
                <div class="col-md-3">
                    <label class="form-label fw-bold">رقم الهاتف</label>
                    <input type="text" name="phone_number" class="form-control" placeholder="ادخل رقم الهاتف" value="<?php echo isset($_GET['phone_number']) ? htmlspecialchars($_GET['phone_number']) : ''; ?>">
                </div>
                <div class="col-md-3">
                    <label class="form-label fw-bold">عنوان IP</label>
                    <input type="text" name="ip_address" class="form-control" placeholder="ادخل عنوان IP" value="<?php echo isset($_GET['ip_address']) ? htmlspecialchars($_GET['ip_address']) : ''; ?>">
                </div>
                <div class="col-md-3">
                    <label class="form-label fw-bold">المنفذ (Port)</label>
                    <input type="text" name="port" class="form-control" placeholder="ادخل رقم المنفذ" value="<?php echo isset($_GET['port']) ? htmlspecialchars($_GET['port']) : ''; ?>">
                </div>
                <div class="col-md-3">
                    <label class="form-label fw-bold">العنوان</label>
                    <input type="text" name="point_name" class="form-control" placeholder="ادخل العنوان" value="<?php echo isset($_GET['point_name']) ? htmlspecialchars($_GET['point_name']) : ''; ?>">
                </div>
                <div class="col-md-3">
                    <label class="form-label fw-bold">الحالة</label>
                    <select name="unms_status" class="form-select">
                        <option value="">الكل</option>
                        <option value="active" <?php echo (isset($_GET['unms_status']) && $_GET['unms_status'] == 'active') ? 'selected' : ''; ?>>نشط</option>
                        <option value="inactive" <?php echo (isset($_GET['unms_status']) && $_GET['unms_status'] == 'inactive') ? 'selected' : ''; ?>>غير نشط</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label fw-bold">نوع الخدمة</label>
                    <select name="service_type_id" class="form-select">
                        <option value="">الكل</option>
                        <?php
                        $service_types_result = $conn->query("SELECT id, name FROM service_types WHERE status = 'active' ORDER BY name");
                        if ($service_types_result) {
                            while ($service_type = $service_types_result->fetch_assoc()):
                        ?>
                            <option value="<?php echo $service_type['id']; ?>" <?php echo (isset($_GET['service_type_id']) && $_GET['service_type_id'] == $service_type['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($service_type['name']); ?>
                            </option>
                        <?php
                            endwhile;
                        }
                        ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label fw-bold">مكان الكابينة</label>
                    <select name="cabinet_location_id" class="form-select">
                        <option value="">الكل</option>
                        <?php
                        $cabinet_locations_result = $conn->query("SELECT id, name FROM cabinet_locations WHERE status = 'active' ORDER BY name");
                        if ($cabinet_locations_result) {
                            while ($cabinet_location = $cabinet_locations_result->fetch_assoc()):
                        ?>
                            <option value="<?php echo $cabinet_location['id']; ?>" <?php echo (isset($_GET['cabinet_location_id']) && $_GET['cabinet_location_id'] == $cabinet_location['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($cabinet_location['name']); ?>
                            </option>
                        <?php
                            endwhile;
                        }
                        ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label fw-bold">مكان البرج</label>
                    <select name="tower_location_id" class="form-select">
                        <option value="">الكل</option>
                        <?php
                        $tower_locations_result = $conn->query("SELECT id, name FROM tower_locations WHERE status = 'active' ORDER BY name");
                        if ($tower_locations_result) {
                            while ($tower_location = $tower_locations_result->fetch_assoc()):
                        ?>
                            <option value="<?php echo $tower_location['id']; ?>" <?php echo (isset($_GET['tower_location_id']) && $_GET['tower_location_id'] == $tower_location['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($tower_location['name']); ?>
                            </option>
                        <?php
                            endwhile;
                        }
                        ?>
                    </select>
                </div>
                <div class="col-md-3">
                    <label class="form-label fw-bold">اسم الفرع</label>
                    <select name="branch_name_id" class="form-select">
                        <option value="">الكل</option>
                        <?php
                        $branch_names_result = $conn->query("SELECT id, name FROM branch_names WHERE status = 'active' ORDER BY name");
                        if ($branch_names_result) {
                            while ($branch_name = $branch_names_result->fetch_assoc()):
                        ?>
                            <option value="<?php echo $branch_name['id']; ?>" <?php echo (isset($_GET['branch_name_id']) && $_GET['branch_name_id'] == $branch_name['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($branch_name['name']); ?>
                            </option>
                        <?php
                            endwhile;
                        }
                        ?>
                    </select>
                </div>
                <div class="col-12 mt-4 text-center">
                    <button type="submit" class="btn btn-primary px-4">
                        <i class="bi bi-search me-2"></i> بحث
                    </button>
                    <a href="<?php echo get_path('/pages/agents.php'); ?>" class="btn btn-secondary px-4 ms-2">
                        <i class="bi bi-arrow-counterclockwise me-2"></i> إعادة تعيين
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- جدول المشتركين -->
    <div class="card animate__animated animate__fadeIn" style="animation-delay: 0.4s">
        <div class="card-body">
            <div class="table-responsive">
                <table id="agentsTable" class="table table-custom table-striped table-animated">
                    <thead>
                        <tr>
                            <th class="cell-shrink">#</th>
                            <th>اسم المشترك</th>
                            <th>رقم الهاتف</th>
                            <th>العنوان</th>
                            <th>الحالة</th>
                            <th>تاريخ الإضافة</th>
                            <th class="cell-action">الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                    <?php
                    // بناء استعلام البحث
                    $sql = "SELECT DISTINCT a.*,
                                   st.name as service_type_name,
                                   cl.name as cabinet_location_name,
                                   tl.name as tower_location_name,
                                   bn.name as branch_name
                            FROM agents a
                            LEFT JOIN service_types st ON a.service_type_id = st.id
                            LEFT JOIN cabinet_locations cl ON a.cabinet_location_id = cl.id
                            LEFT JOIN tower_locations tl ON a.tower_location_id = tl.id
                            LEFT JOIN branch_names bn ON a.branch_name_id = bn.id";
                    
                    // إضافة جدول عناوين IP إذا كان هناك بحث عن طريق IP
                    if (!empty($_GET['ip_address'])) {
                        $sql .= " LEFT JOIN agent_ips ip ON a.id = ip.agent_id";
                    }
                    
                    $sql .= " WHERE 1=1";
                    $params = [];

                    // إضافة شروط البحث
                    if (!empty($_GET['agent_name'])) {
                        $sql .= " AND a.agent_name LIKE ?";
                        $params[] = '%' . $_GET['agent_name'] . '%';
                    }
                    if (!empty($_GET['phone_number'])) {
                        $sql .= " AND a.phone_number LIKE ?";
                        $params[] = '%' . $_GET['phone_number'] . '%';
                    }
                    if (!empty($_GET['ip_address'])) {
                        $sql .= " AND ip.ip_address LIKE ?";
                        $params[] = '%' . $_GET['ip_address'] . '%';
                    }
                    if (!empty($_GET['point_name'])) {
                        $sql .= " AND a.point_name LIKE ?";
                        $params[] = '%' . $_GET['point_name'] . '%';
                    }
                    if (!empty($_GET['port'])) {
                        $sql .= " AND a.port LIKE ?";
                        $params[] = '%' . $_GET['port'] . '%';
                    }
                    if (!empty($_GET['unms_status'])) {
                        $sql .= " AND a.unms_status = ?";
                        $params[] = $_GET['unms_status'];
                    }
                    if (!empty($_GET['service_type_id'])) {
                        $sql .= " AND a.service_type_id = ?";
                        $params[] = $_GET['service_type_id'];
                    }
                    if (!empty($_GET['cabinet_location_id'])) {
                        $sql .= " AND a.cabinet_location_id = ?";
                        $params[] = $_GET['cabinet_location_id'];
                    }
                    if (!empty($_GET['tower_location_id'])) {
                        $sql .= " AND a.tower_location_id = ?";
                        $params[] = $_GET['tower_location_id'];
                    }
                    if (!empty($_GET['branch_name_id'])) {
                        $sql .= " AND a.branch_name_id = ?";
                        $params[] = $_GET['branch_name_id'];
                    }

                    $sql .= " ORDER BY a.id DESC";
                    $result = executeQuery($sql, $params);

                    if ($result && $result->num_rows > 0) {
                        while ($row = $result->fetch_assoc()) {
                    ?>
                        <tr>
                            <td class="cell-shrink"><?php echo $row['id']; ?></td>
                            <td class="cell-primary">
                                <a href="agent_details.php?id=<?php echo $row['id']; ?>" class="fw-bold text-decoration-none">
                                    <?php echo htmlspecialchars($row['agent_name']); ?>
                                </a>
                            </td>
                            <td class="number"><?php echo htmlspecialchars($row['phone_number']); ?></td>
                            <td><?php echo htmlspecialchars($row['point_name']); ?></td>
                            <td>
                                <?php if ($row['unms_status'] == 'active'): ?>
                                    <span class="status-badge status-active">نشط</span>
                                <?php else: ?>
                                    <span class="status-badge status-inactive">غير نشط</span>
                                <?php endif; ?>
                            </td>
                            <td class="date">
                                <?php echo date('Y-m-d', strtotime($row['created_at'])); ?>
                            </td>
                            <td class="cell-action">
                                <div class="btn-group">
                                    <a href="agent_details.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-info" title="عرض التفاصيل">
                                        <i class="bi bi-eye-fill"></i>
                                    </a>
                                    <?php if (check_permission('can_edit_agents')): ?>
                                    <a href="edit_agent.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-primary" title="تعديل">
                                        <i class="bi bi-pencil-fill"></i>
                                    </a>
                                    <?php endif; ?>
                                    <?php if (isset($_SESSION['user_role']) && $_SESSION['user_role'] === 'admin'): ?>
                                    <button type="button" class="btn btn-sm btn-danger delete-agent" data-id="<?php echo $row['id']; ?>" title="حذف">
                                        <i class="bi bi-trash-fill"></i>
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                    <?php
                        }
                    } else {
                    ?>
                        <tr class="no-results-row">
                            <td></td>
                            <td colspan="5" class="text-center table-empty">
                                <div>
                                    <i class="bi bi-inbox-fill d-block mb-3"></i>
                                    <p>لا توجد بيانات للعرض</p>
                                    <a href="add_agent.php" class="btn btn-primary btn-sm">
                                        <i class="bi bi-person-plus-fill me-1"></i> إضافة مشترك جديد
                                    </a>
                                </div>
                            </td>
                            <td></td>
                        </tr>
                    <?php
                    }
                    ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تهيئة DataTables
    <?php if (isset($result) && $result->num_rows > 0): ?>
    const table = $('#agentsTable').DataTable({
        language: {
            url: 'https://cdn.datatables.net/plug-ins/1.11.5/i18n/ar.json',
            emptyTable: 'لا توجد بيانات للعرض'
        },
        dom: 'Bfrtip',
        buttons: [
            {
                extend: 'excel',
                text: '<i class="bi bi-file-earmark-excel-fill"></i> تصدير Excel',
                className: 'btn btn-success',
                exportOptions: {
                    columns: [0, 1, 2, 3, 4, 5, 6, 7]
                }
            },
            {
                extend: 'print',
                text: '<i class="bi bi-printer-fill"></i> طباعة',
                className: 'btn btn-info',
                exportOptions: {
                    columns: [0, 1, 2, 3, 4, 5, 6, 7]
                }
            }
        ],
        responsive: true,
        ordering: true,
        paging: true,
        pageLength: 25,
        lengthMenu: [10, 25, 50, 100],
        autoWidth: false
    });
    <?php endif; ?>

    // معالجة حذف المشترك
    $(document).on('click', '.delete-agent', function() {
        const agentId = $(this).data('id');
        console.log('تم النقر على زر حذف المشترك بمعرف: ' + agentId);
        
        Swal.fire({
            title: 'هل أنت متأكد؟',
            text: "سيتم حذف هذا المشترك نهائياً!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'نعم، قم بالحذف!',
            cancelButtonText: 'إلغاء'
        }).then((result) => {
            if (result.isConfirmed) {
                console.log('تم تأكيد حذف المشترك بمعرف: ' + agentId);
                // استخدام الطريقة المباشرة للحذف
                window.location.href = '<?php echo SITE_URL; ?>/pages/delete_agent_new.php?id=' + agentId;
            }
        });
    });
    // تم استبدال زر إعادة التعيين بعنصر رابط مباشر للصفحة
});
</script>

<?php include '../includes/footer.php'; ?>
