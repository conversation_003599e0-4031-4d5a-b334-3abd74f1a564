<?php
/**
 * ملف خيارات القوائم المنسدلة
 * يحتوي على دوال لجلب خيارات القوائم المنسدلة من قاعدة البيانات
 */

/**
 * جلب خيارات الفروع
 */
function getBranchOptions() {
    global $conn;
    
    $options = [];
    
    // التحقق من وجود الجدول
    if (tableExists('branches')) {
        $sql = "SELECT name, display_name FROM branches WHERE is_active = 1 ORDER BY display_name";
        $result = $conn->query($sql);
        
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $options[] = [
                    'value' => $row['name'],
                    'text' => $row['display_name']
                ];
            }
        }
    }
    
    // إذا لم توجد بيانات، استخدم القيم الافتراضية
    if (empty($options)) {
        $options = [
            ['value' => 'main', 'text' => 'الفرع الرئيسي'],
            ['value' => 'kufa', 'text' => 'فرع الكوفة'],
            ['value' => 'najaf_center', 'text' => 'فرع مركز النجف'],
            ['value' => 'najaf_old', 'text' => 'فرع النجف القديمة'],
            ['value' => 'industrial', 'text' => 'المنطقة الصناعية']
        ];
    }
    
    return $options;
}

/**
 * جلب خيارات أنواع الخدمة
 */
function getServiceTypeOptions() {
    global $conn;
    
    $options = [];
    
    // التحقق من وجود الجدول
    if (tableExists('service_types')) {
        $sql = "SELECT name, display_name FROM service_types WHERE is_active = 1 ORDER BY display_name";
        $result = $conn->query($sql);
        
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $options[] = [
                    'value' => $row['name'],
                    'text' => $row['display_name']
                ];
            }
        }
    }
    
    // إذا لم توجد بيانات، استخدم القيم الافتراضية
    if (empty($options)) {
        $options = [
            ['value' => 'internet', 'text' => 'إنترنت'],
            ['value' => 'wifi', 'text' => 'واي فاي'],
            ['value' => 'fiber', 'text' => 'فايبر'],
            ['value' => 'wireless', 'text' => 'لاسلكي'],
            ['value' => 'mixed', 'text' => 'مختلط']
        ];
    }
    
    return $options;
}

/**
 * جلب خيارات مواقع الأبراج
 */
function getTowerLocationOptions() {
    global $conn;
    
    $options = [];
    
    // التحقق من وجود الجدول
    if (tableExists('tower_locations')) {
        $sql = "SELECT name, display_name FROM tower_locations WHERE is_active = 1 ORDER BY display_name";
        $result = $conn->query($sql);
        
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $options[] = [
                    'value' => $row['name'],
                    'text' => $row['display_name']
                ];
            }
        }
    }
    
    // إذا لم توجد بيانات، استخدم القيم الافتراضية
    if (empty($options)) {
        $options = [
            ['value' => 'main_tower', 'text' => 'البرج الرئيسي'],
            ['value' => 'kufa_tower', 'text' => 'برج الكوفة'],
            ['value' => 'industrial_tower', 'text' => 'برج المنطقة الصناعية'],
            ['value' => 'najaf_old_tower', 'text' => 'برج النجف القديمة'],
            ['value' => 'university_tower', 'text' => 'برج الجامعة']
        ];
    }
    
    return $options;
}

/**
 * جلب خيارات التبعية
 */
function getBelongsToOptions() {
    global $conn;
    
    $options = [];
    
    // التحقق من وجود الجدول
    if (tableExists('belongs_to_options')) {
        $sql = "SELECT name, display_name FROM belongs_to_options WHERE is_active = 1 ORDER BY display_name";
        $result = $conn->query($sql);
        
        if ($result && $result->num_rows > 0) {
            while ($row = $result->fetch_assoc()) {
                $options[] = [
                    'value' => $row['name'],
                    'text' => $row['display_name']
                ];
            }
        }
    }
    
    // إذا لم توجد بيانات، استخدم القيم الافتراضية
    if (empty($options)) {
        $options = [
            ['value' => 'company', 'text' => 'الشركة'],
            ['value' => 'agent', 'text' => 'وكيل'],
            ['value' => 'branch', 'text' => 'فرع'],
            ['value' => 'partner', 'text' => 'شريك'],
            ['value' => 'independent', 'text' => 'مستقل']
        ];
    }
    
    return $options;
}

/**
 * دالة مساعدة لإنشاء خيارات HTML للقائمة المنسدلة
 */
function renderSelectOptions($options, $selected_value = '') {
    $html = '';
    foreach ($options as $option) {
        $selected = ($option['value'] == $selected_value) ? 'selected' : '';
        $html .= "<option value='{$option['value']}' $selected>{$option['text']}</option>";
    }
    return $html;
}

/**
 * دالة للتحقق من وجود جدول (إذا لم تكن موجودة في init.php)
 */
if (!function_exists('tableExists')) {
    function tableExists($table_name) {
        global $conn;
        $result = $conn->query("SHOW TABLES LIKE '$table_name'");
        return $result && $result->num_rows > 0;
    }
}

/**
 * جلب خيارات الريبيترز (المكررات)
 */
function getRepeaterOptions() {
    global $conn;
    
    $options = [];
    
    $sql = "SELECT id, repeater_name FROM repeaters WHERE is_active = 1 ORDER BY repeater_name";
    $result = $conn->query($sql);
    
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $options[] = [
                'value' => $row['id'],
                'text' => $row['repeater_name']
            ];
        }
    }
    
    return $options;
}

/**
 * جلب خيارات أنواع الأجهزة
 */
function getDeviceTypeOptions() {
    global $conn;
    
    $options = [];
    
    $sql = "SELECT id, type_name FROM device_types WHERE is_active = 1 ORDER BY type_name";
    $result = $conn->query($sql);
    
    if ($result && $result->num_rows > 0) {
        while ($row = $result->fetch_assoc()) {
            $options[] = [
                'value' => $row['id'],
                'text' => $row['type_name']
            ];
        }
    }
    
    return $options;
}

/**
 * دالة شاملة لجلب جميع خيارات القوائم المنسدلة
 */
function getAllDropdownOptions() {
    return [
        'branches' => getBranchOptions(),
        'service_types' => getServiceTypeOptions(),
        'tower_locations' => getTowerLocationOptions(),
        'belongs_to' => getBelongsToOptions(),
        'repeaters' => getRepeaterOptions(),
        'device_types' => getDeviceTypeOptions()
    ];
}
?>
