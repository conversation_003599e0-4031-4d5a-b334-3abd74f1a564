<?php
require_once '../../includes/init.php';

if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    http_response_code(400);
    exit('Invalid agent ID');
}

$agent_id = intval($_GET['id']);

// بيانات التوفر لآخر 7 أيام
$uptime_query = "SELECT 
    date_recorded,
    (successful_checks * 100.0 / NULLIF(total_checks, 0)) as uptime,
    average_response_time
FROM agent_uptime_stats
WHERE agent_id = ?
AND date_recorded >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
ORDER BY date_recorded";

$stmt = $conn->prepare($uptime_query);
$stmt->bind_param('i', $agent_id);
$stmt->execute();
$result = $stmt->get_result();

$dates = [];
$uptime = [];
$response_times = [];

while ($row = $result->fetch_assoc()) {
    $dates[] = $row['date_recorded'];
    $uptime[] = round($row['uptime'], 2);
    $response_times[] = $row['average_response_time'];
}

$response = [
    'dates' => $dates,
    'uptime' => $uptime,
    'response_times' => $response_times
];

header('Content-Type: application/json');
echo json_encode($response);
