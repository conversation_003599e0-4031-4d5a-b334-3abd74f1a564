<?php
require_once 'init.php';

class DatabaseBackup {
    private $conn;
    private $backup_dir;
    private $backup_file;
    
    public function __construct($conn) {
        $this->conn = $conn;
        $this->backup_dir = dirname(__DIR__) . '/backups';
        
        // إنشاء مجلد النسخ الاحتياطي إذا لم يكن موجوداً
        if (!file_exists($this->backup_dir)) {
            mkdir($this->backup_dir, 0777, true);
        }
        
        // إنشاء اسم الملف بالتاريخ والوقت
        $this->backup_file = $this->backup_dir . '/backup_' . date('Y-m-d_H-i-s') . '.sql';
    }
    
    public function backup() {
        try {
            $tables = [];
            $result = $this->conn->query("SHOW TABLES");
            while ($row = $result->fetch_row()) {
                $tables[] = $row[0];
            }
            
            $return = "-- نسخة احتياطية تم إنشاؤها في " . date('Y-m-d H:i:s') . "\n\n";
            
            // حفظ محتوى كل جدول
            foreach ($tables as $table) {
                $result = $this->conn->query("SELECT * FROM $table");
                $num_fields = $result->field_count;
                
                $return .= "DROP TABLE IF EXISTS $table;\n";
                
                // إنشاء هيكل الجدول
                $row2 = $this->conn->query("SHOW CREATE TABLE $table")->fetch_row();
                $return .= $row2[1] . ";\n\n";
                
                // إضافة البيانات
                while ($row = $result->fetch_row()) {
                    $return .= "INSERT INTO $table VALUES(";
                    for ($j = 0; $j < $num_fields; $j++) {
                        $row[$j] = addslashes($row[$j]);
                        $row[$j] = str_replace("\n", "\\n", $row[$j]);
                        if (isset($row[$j])) {
                            $return .= '"' . $row[$j] . '"';
                        } else {
                            $return .= '""';
                        }
                        if ($j < ($num_fields - 1)) {
                            $return .= ',';
                        }
                    }
                    $return .= ");\n";
                }
                $return .= "\n\n";
            }
            
            // حفظ الملف
            file_put_contents($this->backup_file, $return);
            
            // حذف النسخ الاحتياطية القديمة (الاحتفاظ بآخر 5 نسخ فقط)
            $this->cleanOldBackups();
            
            // تسجيل النشاط
            log_activity("نسخ احتياطي", "تم إنشاء نسخة احتياطية جديدة: " . basename($this->backup_file));
            
            return [
                'success' => true,
                'message' => 'تم إنشاء النسخة الاحتياطية بنجاح',
                'file' => basename($this->backup_file)
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء النسخة الاحتياطية: ' . $e->getMessage()
            ];
        }
    }
    
    private function cleanOldBackups() {
        $files = glob($this->backup_dir . '/backup_*.sql');
        if (count($files) > 5) {
            // ترتيب الملفات حسب تاريخ التعديل
            usort($files, function($a, $b) {
                return filemtime($b) - filemtime($a);
            });
            
            // حذف الملفات القديمة
            $old_files = array_slice($files, 5);
            foreach ($old_files as $file) {
                unlink($file);
            }
        }
    }
    
    public function getBackupsList() {
        $files = glob($this->backup_dir . '/backup_*.sql');
        $backups = [];
        
        foreach ($files as $file) {
            $backups[] = [
                'name' => basename($file),
                'size' => $this->formatSize(filesize($file)),
                'date' => date('Y-m-d H:i:s', filemtime($file))
            ];
        }
        
        // ترتيب النسخ الاحتياطية من الأحدث إلى الأقدم
        usort($backups, function($a, $b) {
            return strtotime($b['date']) - strtotime($a['date']);
        });
        
        return $backups;
    }
    
    private function formatSize($size) {
        $units = ['B', 'KB', 'MB', 'GB'];
        $i = 0;
        while ($size >= 1024 && $i < count($units) - 1) {
            $size /= 1024;
            $i++;
        }
        return round($size, 2) . ' ' . $units[$i];
    }
}
?>
