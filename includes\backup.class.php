<?php
class DatabaseBackup {
    private $conn;
    private $backup_dir;
    private $settings;

    public function __construct($conn) {
        $this->conn = $conn;
        $this->backup_dir = dirname(__DIR__) . '/backups/';
        $this->ensureBackupDir();
        $this->loadSettings();
    }

    // التأكد من وجود مجلد النسخ الاحتياطي
    private function ensureBackupDir() {
        if (!file_exists($this->backup_dir)) {
            mkdir($this->backup_dir, 0755, true);
        }
    }

    // تحميل إعدادات النسخ الاحتياطي
    private function loadSettings() {
        try {
            // التأكد من وجود الجدول
            $this->ensureSettingsTable();
            
            // التحقق من وجود إعدادات
            $result = $this->conn->query("SELECT * FROM backup_settings ORDER BY id DESC LIMIT 1");
            if (!$result) {
                throw new Exception("خطأ في استعلام قاعدة البيانات: " . $this->conn->error);
            }
            
            $this->settings = $result->fetch_assoc();
            
            // إذا لم تكن هناك إعدادات، قم بإنشاء إعدادات افتراضية
            if (!$this->settings) {
                // التحقق من صحة الجدول
                $check = $this->conn->query("DESCRIBE backup_settings");
                if (!$check) {
                    throw new Exception("جدول backup_settings غير موجود أو به مشكلة: " . $this->conn->error);
                }

                $sql = "INSERT INTO backup_settings 
                        (frequency, time, retention_days, created_at) 
                        VALUES 
                        ('daily', '00:00:00', 30, NOW())";
                
                if (!$this->conn->query($sql)) {
                    throw new Exception("خطأ في إدخال الإعدادات الافتراضية: " . $this->conn->error);
                }

                // إعادة تحميل الإعدادات
                $result = $this->conn->query("SELECT * FROM backup_settings ORDER BY id DESC LIMIT 1");
                if (!$result) {
                    throw new Exception("خطأ في إعادة تحميل الإعدادات: " . $this->conn->error);
                }
                
                $this->settings = $result->fetch_assoc();
                if (!$this->settings) {
                    throw new Exception("فشل في استرجاع الإعدادات بعد إنشائها");
                }
            }
        } catch (Exception $e) {
            error_log("خطأ في تحميل إعدادات النسخ الاحتياطي: " . $e->getMessage());
            throw $e;
        }
    }

    // التأكد من وجود جدول الإعدادات
    private function ensureSettingsTable() {
        try {
            $sql = "CREATE TABLE IF NOT EXISTS backup_settings (
                id INT PRIMARY KEY AUTO_INCREMENT,
                frequency ENUM('daily', 'weekly', 'monthly') NOT NULL DEFAULT 'daily',
                backup_time TIME NOT NULL DEFAULT '00:00:00',
                retention_days INT NOT NULL DEFAULT 30,
                last_backup DATETIME,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
            
            if (!$this->conn->query($sql)) {
                throw new Exception("فشل في إنشاء جدول backup_settings: " . $this->conn->error);
            }
        } catch (Exception $e) {
            error_log("خطأ في إنشاء جدول الإعدادات: " . $e->getMessage());
            throw $e;
        }
    }

    // إنشاء نسخة احتياطية
    public function createBackup() {
        try {
            // إعادة تحميل الإعدادات للتأكد من وجودها
            $this->loadSettings();
            
            if (!$this->settings || !isset($this->settings['id'])) {
                throw new Exception("لم يتم العثور على إعدادات النسخ الاحتياطي");
            }

            $tables = [];
            $result = $this->conn->query("SHOW TABLES");
            while ($row = $result->fetch_array()) {
                $tables[] = $row[0];
            }

            $output = '';
            foreach ($tables as $table) {
                $result = $this->conn->query("SELECT * FROM " . $table);
                $numFields = $result->field_count;

                $output .= "DROP TABLE IF EXISTS " . $table . ";\n";
                $row2 = $this->conn->query("SHOW CREATE TABLE " . $table)->fetch_row();
                $output .= $row2[1] . ";\n\n";

                while ($row = $result->fetch_row()) {
                    $output .= "INSERT INTO " . $table . " VALUES(";
                    for ($j = 0; $j < $numFields; $j++) {
                        $row[$j] = addslashes($row[$j]);
                        $row[$j] = str_replace("\n", "\\n", $row[$j]);
                        if (isset($row[$j])) {
                            $output .= '"' . $row[$j] . '"';
                        } else {
                            $output .= '""';
                        }
                        if ($j < ($numFields - 1)) {
                            $output .= ',';
                        }
                    }
                    $output .= ");\n";
                }
                $output .= "\n\n";
            }

            $backup_file = $this->backup_dir . 'backup_' . date('Y-m-d_H-i-s') . '.sql';
            if (file_put_contents($backup_file, $output) === false) {
                throw new Exception("فشل في كتابة ملف النسخة الاحتياطية");
            }

            // تحديث وقت آخر نسخة احتياطية
            $sql = "UPDATE backup_settings SET last_backup = NOW() WHERE id = ?";
            $stmt = $this->conn->prepare($sql);
            $stmt->bind_param("i", $this->settings['id']);
            $stmt->execute();
            $stmt->close();

            // حذف النسخ القديمة
            $this->cleanOldBackups();

            return ['success' => true, 'file' => basename($backup_file)];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    // استعادة نسخة احتياطية
    public function restoreBackup($filename) {
        try {
            $file = $this->backup_dir . basename($filename);
            if (!file_exists($file)) {
                throw new Exception("ملف النسخة الاحتياطية غير موجود");
            }

            $sql = file_get_contents($file);
            $queries = array_filter(array_map('trim', explode(';', $sql)));

            foreach ($queries as $query) {
                if (!empty($query)) {
                    $this->conn->query($query);
                }
            }

            return ['success' => true];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    // الحصول على قائمة النسخ الاحتياطية
    public function getBackupsList() {
        $backups = [];
        if (is_dir($this->backup_dir)) {
            foreach (scandir($this->backup_dir) as $file) {
                if ($file !== '.' && $file !== '..' && pathinfo($file, PATHINFO_EXTENSION) === 'sql') {
                    $filepath = $this->backup_dir . $file;
                    $backups[] = [
                        'filename' => $file,
                        'size' => filesize($filepath),
                        'created_at' => date("Y-m-d H:i:s", filemtime($filepath))
                    ];
                }
            }
        }
        // ترتيب النسخ من الأحدث إلى الأقدم
        usort($backups, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });
        return $backups;
    }

    // حذف نسخة احتياطية
    public function deleteBackup($filename) {
        try {
            $file = $this->backup_dir . basename($filename);
            if (!file_exists($file)) {
                throw new Exception("الملف غير موجود");
            }
            unlink($file);
            return ['success' => true];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    // حذف النسخ القديمة
    private function cleanOldBackups() {
        $retention_days = $this->settings['retention_days'] ?? 30;
        $cutoff = strtotime("-$retention_days days");

        foreach ($this->getBackupsList() as $backup) {
            if (strtotime($backup['created_at']) < $cutoff) {
                $this->deleteBackup($backup['filename']);
            }
        }
    }

    // تحديث إعدادات النسخ الاحتياطي
    public function updateSettings($frequency, $time, $retention_days) {
        try {
            // التحقق من صحة التكرار
            if (!in_array($frequency, ['daily', 'weekly', 'monthly'])) {
                throw new Exception("قيمة التكرار غير صالحة");
            }

            // التحقق من صحة الوقت
            if (!preg_match('/^([01][0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9]$/', $time)) {
                throw new Exception("صيغة الوقت غير صحيحة. يجب أن تكون HH:MM:SS");
            }

            // التحقق من صحة أيام الاحتفاظ
            if (!is_numeric($retention_days) || $retention_days < 1) {
                throw new Exception("عدد أيام الاحتفاظ يجب أن يكون رقماً موجباً");
            }

            $stmt = $this->conn->prepare("
                INSERT INTO backup_settings (frequency, time, retention_days)
                VALUES (?, ?, ?)
            ");
            
            if (!$stmt) {
                throw new Exception("خطأ في إعداد الاستعلام: " . $this->conn->error);
            }

            $stmt->bind_param("ssi", $frequency, $time, $retention_days);
            
            if (!$stmt->execute()) {
                throw new Exception("خطأ في تحديث الإعدادات: " . $stmt->error);
            }

            $stmt->close();
            
            // إعادة تحميل الإعدادات
            $this->loadSettings();
            
            return ['success' => true];
        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    // الحصول على الإعدادات الحالية
    public function getSettings() {
        return $this->settings;
    }
}
