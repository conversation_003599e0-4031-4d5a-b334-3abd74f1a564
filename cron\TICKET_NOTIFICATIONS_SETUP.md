# إعداد إشعارات التذاكر التلقائية

## نظرة عامة
يحتوي نظام التذاكر على نظام إشعارات متكامل مع Telegram يرسل إشعارات فورية ويومية عن حالة التذاكر.

## أنواع الإشعارات

### 1. الإشعارات الفورية
- **تذكرة جديدة**: عند إنشاء تذكرة جديدة
- **تحديث الحالة**: عند تغيير حالة التذكرة
- **تعليق جديد**: عند إضافة تعليق عام (غير داخلي)
- **مرفق جديد**: عند رفع ملف مرفق

### 2. الإشعارات اليومية (Cron Job)
- **التقرير اليومي**: إحصائيات التذاكر لليوم الحالي والوضع العام
- **التذاكر المتأخرة**: تذكير بالتذاكر المفتوحة لأكثر من 7 أيام
- **التذاكر العاجلة**: تنبيه خاص للتذاكر عالية الأولوية المفتوحة

## إعداد Cron Job

### في Windows (XAMPP/Laragon)

1. **إنشاء ملف batch**:
```batch
@echo off
cd /d "C:\laragon\www\agents_management"
C:\laragon\bin\php\php-8.1.10-Win32-vs16-x64\php.exe cron\ticket_notifications.php >> logs\ticket_cron.log 2>&1
```

2. **حفظ الملف باسم**: `ticket_notifications.bat`

3. **إعداد Task Scheduler**:
   - افتح Task Scheduler
   - اختر "Create Basic Task"
   - اسم المهمة: "Ticket Notifications"
   - التكرار: Daily
   - الوقت: 09:00 AM (أو الوقت المفضل)
   - Action: Start a program
   - Program: مسار ملف `ticket_notifications.bat`

### في Linux

1. **تعيين الصلاحيات**:
```bash
sudo chmod +x /var/www/html/agents_management/cron/ticket_notifications.php
sudo chmod -R 755 /var/www/html/agents_management/logs
sudo chown -R www-data:www-data /var/www/html/agents_management/logs
```

2. **إضافة Cron Job**:
```bash
sudo crontab -e
```

3. **إضافة السطر التالي** (تشغيل يومي الساعة 9 صباحاً):
```bash
0 9 * * * /usr/bin/php /var/www/html/agents_management/cron/ticket_notifications.php >> /var/www/html/agents_management/logs/ticket_cron.log 2>&1
```

## إعدادات Telegram

تأكد من أن إعدادات Telegram محدثة في ملف `monitoring/config.php`:

```php
// إعدادات Telegram
define('TELEGRAM_BOT_TOKEN', 'YOUR_BOT_TOKEN');
define('TELEGRAM_CHAT_ID', 'YOUR_CHAT_ID');
```

## اختبار النظام

### اختبار الإشعارات الفورية
1. قم بإنشاء تذكرة جديدة
2. أضف تعليق على تذكرة موجودة
3. غير حالة تذكرة
4. ارفع مرفق لتذكرة

### اختبار الإشعارات اليومية
```bash
# تشغيل يدوي للاختبار
php cron/ticket_notifications.php
```

## ملفات السجلات

- **السجل العام**: `logs/ticket_notifications.log`
- **سجل Cron**: `logs/ticket_cron.log`
- **سجل أخطاء PHP**: `logs/php_errors.log`

## استكشاف الأخطاء

### مشاكل شائعة:

1. **عدم وصول الإشعارات**:
   - تحقق من صحة TELEGRAM_BOT_TOKEN و TELEGRAM_CHAT_ID
   - تأكد من أن البوت مضاف للمحادثة
   - راجع ملف السجل للأخطاء

2. **عدم تشغيل Cron Job**:
   - تحقق من صلاحيات الملفات
   - تأكد من مسار PHP الصحيح
   - راجع سجل Cron

3. **أخطاء قاعدة البيانات**:
   - تأكد من وجود جداول التذاكر
   - تحقق من صحة اتصال قاعدة البيانات

## تخصيص الإشعارات

يمكن تخصيص رسائل الإشعارات من خلال تعديل ملف `includes/ticket_notifications.php`:

- تغيير نصوص الرسائل
- إضافة معلومات إضافية
- تعديل شروط الإرسال
- تغيير تنسيق الرسائل

## الأمان

- تأكد من حماية ملف `monitoring/config.php`
- لا تشارك معلومات البوت مع أشخاص غير مخولين
- راجع السجلات بانتظام للتأكد من عدم وجود محاولات غير مصرح بها

## الدعم

في حالة وجود مشاكل:
1. راجع ملفات السجلات
2. تأكد من إعدادات Telegram
3. اختبر الاتصال بقاعدة البيانات
4. تحقق من صلاحيات الملفات
