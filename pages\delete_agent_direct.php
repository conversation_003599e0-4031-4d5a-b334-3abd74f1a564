<?php
require_once '../includes/init.php';

// التحقق من تسجيل الدخول
check_login();

// التحقق من الصلاحيات - فقط المدير يمكنه حذف المشتركين
if (!isset($_SESSION['user_role']) || $_SESSION['user_role'] !== 'admin') {
    $_SESSION['error'] = "ليس لديك صلاحية لحذف المشتركين";
    redirect(SITE_URL . '/pages/agents.php');
}

// التحقق من وجود معرف المشترك
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    $_SESSION['error'] = "معرف المشترك غير صحيح";
    redirect(SITE_URL . '/pages/agents.php');
}

$agent_id = intval($_GET['id']);

// التحقق من وجود المشترك
$check_sql = "SELECT id FROM agents WHERE id = ?";
$check_stmt = $conn->prepare($check_sql);
$check_stmt->bind_param('i', $agent_id);
$check_stmt->execute();
$check_result = $check_stmt->get_result();

if ($check_result->num_rows === 0) {
    $_SESSION['error'] = "المشترك غير موجود";
    redirect(SITE_URL . '/pages/agents.php');
}

// بدء المعاملة
$conn->begin_transaction();

try {
    // حذف عناوين IP المرتبطة بالمشترك أولاً
    $stmt = $conn->prepare("DELETE FROM agent_ips WHERE agent_id = ?");
    $stmt->bind_param('i', $agent_id);
    $stmt->execute();

    // حذف المشترك
    $stmt = $conn->prepare("DELETE FROM agents WHERE id = ?");
    $stmt->bind_param('i', $agent_id);
    $stmt->execute();

    // تأكيد المعاملة
    $conn->commit();
    
    // إضافة رسالة نجاح
    $_SESSION['success'] = "تم حذف المشترك بنجاح";
    
} catch (Exception $e) {
    // التراجع عن المعاملة في حالة الخطأ
    $conn->rollback();
    $_SESSION['error'] = "حدث خطأ أثناء حذف المشترك: " . $e->getMessage();
}

// إعادة التوجيه إلى صفحة المشتركين
redirect(SITE_URL . '/pages/agents.php');
