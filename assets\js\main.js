// تأكيد الحذف
function confirmDelete(message) {
    return confirm(message || 'هل أنت متأكد من الحذف؟');
}

// تنسيق التاريخ
function formatDate(date) {
    return new Date(date).toLocaleDateString('ar-SA');
}

// إظهار رسالة نجاح
function showSuccess(message) {
    const alert = document.createElement('div');
    alert.className = 'alert alert-success alert-dismissible fade show';
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.querySelector('.container').insertBefore(alert, document.querySelector('.container').firstChild);
}

// إظهار رسالة خطأ
function showError(message) {
    const alert = document.createElement('div');
    alert.className = 'alert alert-danger alert-dismissible fade show';
    alert.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.querySelector('.container').insertBefore(alert, document.querySelector('.container').firstChild);
}

// تحقق من صحة عنوان IP
function validateIP(ip) {
    const regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
    return regex.test(ip);
}

// إعداد الجداول للفرز والبحث
document.addEventListener('DOMContentLoaded', function() {
    const tables = document.querySelectorAll('.table');
    tables.forEach(table => {
        const headers = table.querySelectorAll('th');
        headers.forEach((header, index) => {
            header.addEventListener('click', () => {
                sortTable(table, index);
            });
            header.style.cursor = 'pointer';
        });
    });
});

// دالة فرز الجدول
function sortTable(table, column) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const isNumeric = rows.every(row => {
        const cell = row.cells[column].textContent;
        return !isNaN(cell) || cell.trim() === '';
    });

    rows.sort((a, b) => {
        let aVal = a.cells[column].textContent;
        let bVal = b.cells[column].textContent;

        if (isNumeric) {
            return Number(aVal) - Number(bVal);
        } else {
            return aVal.localeCompare(bVal, 'ar');
        }
    });

    if (table.getAttribute('data-sort') === 'asc') {
        rows.reverse();
        table.setAttribute('data-sort', 'desc');
    } else {
        table.setAttribute('data-sort', 'asc');
    }

    tbody.innerHTML = '';
    rows.forEach(row => tbody.appendChild(row));
}

// دالة البحث في الجدول
function searchTable(input, tableId) {
    const filter = input.value.toLowerCase();
    const table = document.getElementById(tableId);
    const rows = table.getElementsByTagName('tr');

    for (let i = 1; i < rows.length; i++) {
        const cells = rows[i].getElementsByTagName('td');
        let found = false;

        for (let j = 0; j < cells.length; j++) {
            const cell = cells[j];
            if (cell) {
                const text = cell.textContent || cell.innerText;
                if (text.toLowerCase().indexOf(filter) > -1) {
                    found = true;
                    break;
                }
            }
        }

        rows[i].style.display = found ? '' : 'none';
    }
}

// تحديث حالة الاتصال
function updateConnectionStatus(element, status) {
    element.className = 'badge bg-' + (status ? 'success' : 'danger');
    element.textContent = status ? 'متصل' : 'غير متصل';
}

// تصدير الجدول إلى Excel
function exportToExcel(tableId, filename) {
    const table = document.getElementById(tableId);
    const ws = XLSX.utils.table_to_sheet(table, {raw: true});
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, "Sheet1");
    XLSX.writeFile(wb, filename + '.xlsx');
}

// إدارة الوضع الليلي
document.addEventListener('DOMContentLoaded', function() {
    const themeSwitch = document.getElementById('themeSwitch');
    
    // تحقق من الوضع المحفوظ
    const savedTheme = localStorage.getItem('theme') || 'light';
    document.documentElement.setAttribute('data-theme', savedTheme);
    
    // التحقق من وجود العنصر قبل تعيين خاصية checked
    if (themeSwitch) {
        themeSwitch.checked = savedTheme === 'dark';
    }

    // تحديث الأيقونة عند تحميل الصفحة
    if (typeof updateThemeIcon === 'function') {
        updateThemeIcon(savedTheme === 'dark');
    }
    
    // معالج تغيير الوضع
    if (themeSwitch) {
        themeSwitch.addEventListener('change', function(e) {
            const isDark = e.target.checked;
            const theme = isDark ? 'dark' : 'light';
            
            document.documentElement.setAttribute('data-theme', theme);
            localStorage.setItem('theme', theme);
            
            // تحديث الأيقونة
            if (typeof updateThemeIcon === 'function') {
                updateThemeIcon(isDark);
            }
            
            // تسجيل النشاط
            fetch('../includes/log_activity.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: `action=تغيير_المظهر&description=تم التغيير إلى الوضع ${isDark ? 'الليلي' : 'النهاري'}`
            });
        });
    }

// تحديث أيقونة الوضع
function updateThemeIcon(isDark) {
    const sunIcon = document.querySelector('.theme-switch .bi-sun');
    const moonIcon = document.querySelector('.theme-switch .bi-moon');
    
    if (isDark) {
        sunIcon.style.opacity = '0.5';
        moonIcon.style.opacity = '1';
    } else {
        sunIcon.style.opacity = '1';
        moonIcon.style.opacity = '0.5';
    }
}
