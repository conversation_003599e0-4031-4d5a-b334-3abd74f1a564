<?php
// تأكد من عدم وجود مخرجات قبل هذا السطر
ob_start();
require_once '../includes/init.php';
$page_title = "إضافة مشترك جديد";
include '../includes/header.php';

// تهيئة متغير الخطأ والنجاح
$error = null;
$success = null;

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $agent_name = clean($_POST['agent_name']);
        $phone_number = clean($_POST['phone_number']);
        $secondary_phone_number = clean($_POST['secondary_phone_number'] ?? '');

        $point_name = clean($_POST['point_name']);

        // معالجة أسماء المستخدمين المتعددة
        $primary_username = '';
        $primary_password = '';
        if (!empty($_POST['usernames']) && is_array($_POST['usernames'])) {
            $primary_user_index = $_POST['primary_user'] ?? 0;
            if (isset($_POST['usernames'][$primary_user_index])) {
                $primary_username = clean($_POST['usernames'][$primary_user_index]['username'] ?? '');
                $primary_password = clean($_POST['usernames'][$primary_user_index]['password'] ?? '');
            }
        }

        $username = $primary_username;
        $password = $primary_password;

        $rp_sub = clean($_POST['rp_sub']);

        $port = clean($_POST['port']);
        $device_ownership = clean($_POST['device_ownership']);
        $unms_status = clean($_POST['unms_status']);
        $notes = clean($_POST['notes']);
        $mac_address = clean($_POST['mac_address'] ?? '');
        $ssid = clean($_POST['ssid'] ?? '');
        


        // إضافة الحقول الجديدة
        $serial_number = clean($_POST['serial_number'] ?? '');
        $sn_onu = clean($_POST['sn_onu'] ?? '');
        $sector_number = clean($_POST['sector_number'] ?? '');
        $bill_price = !empty($_POST['bill_price']) ? (float)$_POST['bill_price'] : null;
        $port_id = !empty($_POST['port_id']) ? (int)$_POST['port_id'] : null;
        $cabinet_location_id = !empty($_POST['cabinet_location_id']) ? (int)$_POST['cabinet_location_id'] : null;
        $discount_id = !empty($_POST['discount_id']) ? (int)$_POST['discount_id'] : null;
        $service_type_id = !empty($_POST['service_type_id']) ? (int)$_POST['service_type_id'] : null;
        $device_ownership_id = !empty($_POST['device_ownership_id']) ? (int)$_POST['device_ownership_id'] : null;
        $belongs_to_id = !empty($_POST['belongs_to_id']) ? (int)$_POST['belongs_to_id'] : null;
        $branch_name_id = !empty($_POST['branch_name_id']) ? (int)$_POST['branch_name_id'] : null;
        $tower_location_id = !empty($_POST['tower_location_id']) ? (int)$_POST['tower_location_id'] : null;
        $latitude = !empty($_POST['latitude']) ? (float)$_POST['latitude'] : null;
        $longitude = !empty($_POST['longitude']) ? (float)$_POST['longitude'] : null;

        // بدء المعاملة
        $conn->begin_transaction();

        // إدخال بيانات المشترك الأساسية
        $sql = "INSERT INTO agents (
            agent_name, phone_number, secondary_phone_number, point_name, username, password, rp_sub,
            port, device_ownership, unms_status, notes, mac_address, ssid,
            serial_number, sn_onu, sector_number, bill_price, port_id, cabinet_location_id,
            discount_id, service_type_id, device_ownership_id, belongs_to_id, branch_name_id, tower_location_id,
            latitude, longitude
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $conn->prepare($sql);
        if ($stmt === false) {
            throw new Exception("خطأ في إعداد الاستعلام: " . $conn->error);
        }

        $result = $stmt->bind_param(
            "ssssssssssssssssdiiiiiiiidd",
            $agent_name, $phone_number, $secondary_phone_number, $point_name, $username, $password, $rp_sub,
            $port, $device_ownership, $unms_status, $notes, $mac_address, $ssid,
            $serial_number, $sn_onu, $sector_number, $bill_price, $port_id, $cabinet_location_id,
            $discount_id, $service_type_id, $device_ownership_id, $belongs_to_id, $branch_name_id, $tower_location_id,
            $latitude, $longitude
        );

        if ($result === false) {
            throw new Exception("خطأ في ربط المعلمات: " . $stmt->error);
        }
        
        if (!$stmt->execute()) {
            throw new Exception("فشل في إضافة المشترك: " . $stmt->error);
        }
        
        $agent_id = $conn->insert_id;

        // إدخال عناوين IP
        if (!empty($_POST['ip_addresses']) && is_array($_POST['ip_addresses'])) {
            $ip_sql = "INSERT INTO agent_ips (agent_id, ip_address, ip_description, username, password, is_primary) VALUES (?, ?, ?, ?, ?, ?)";
            $ip_stmt = $conn->prepare($ip_sql);
            if ($ip_stmt === false) {
                throw new Exception("خطأ في إعداد استعلام IP: " . $conn->error);
            }
            
            foreach ($_POST['ip_addresses'] as $index => $ip) {
                if (empty($ip['address'])) continue;
                
                $is_primary = isset($_POST['primary_ip']) && $_POST['primary_ip'] == $index ? 1 : 0;
                $ip_description = clean($ip['description']);
                $ip_username = clean($ip['username'] ?? '');
                $ip_password = clean($ip['password'] ?? '');
                
                if (!$ip_stmt->bind_param("issssi", $agent_id, $ip['address'], $ip_description, $ip_username, $ip_password, $is_primary)) {
                    throw new Exception("خطأ في ربط معلمات IP: " . $ip_stmt->error);
                }
                
                if (!$ip_stmt->execute()) {
                    throw new Exception("فشل في إضافة عنوان IP: " . $ip_stmt->error);
                }
            }
        }

        // إدخال أسماء المستخدمين المتعددة
        if (!empty($_POST['usernames']) && is_array($_POST['usernames'])) {
            // إنشاء جدول أسماء المستخدمين إذا لم يكن موجوداً
            $create_table_sql = "CREATE TABLE IF NOT EXISTS agent_usernames (
                id INT AUTO_INCREMENT PRIMARY KEY,
                agent_id INT NOT NULL,
                username VARCHAR(255) NOT NULL,
                password VARCHAR(255) NOT NULL,
                is_primary TINYINT(1) DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE
            )";
            $conn->query($create_table_sql);

            $user_sql = "INSERT INTO agent_usernames (agent_id, username, password, is_primary) VALUES (?, ?, ?, ?)";
            $user_stmt = $conn->prepare($user_sql);
            if ($user_stmt === false) {
                throw new Exception("خطأ في إعداد استعلام أسماء المستخدمين: " . $conn->error);
            }

            $primary_user_index = $_POST['primary_user'] ?? 0;

            foreach ($_POST['usernames'] as $index => $user) {
                if (empty($user['username'])) continue;

                $is_primary = ($primary_user_index == $index) ? 1 : 0;
                $user_username = clean($user['username']);
                $user_password = clean($user['password'] ?? '');

                if (!$user_stmt->bind_param("issi", $agent_id, $user_username, $user_password, $is_primary)) {
                    throw new Exception("خطأ في ربط معلمات أسماء المستخدمين: " . $user_stmt->error);
                }

                if (!$user_stmt->execute()) {
                    throw new Exception("فشل في إضافة اسم المستخدم: " . $user_stmt->error);
                }
            }
        }

        // تأكيد المعاملة
        $conn->commit();
        
        log_activity("إضافة وكيل", "تمت إضافة مشترك جديد: $agent_name");
        $success = "تم إضافة المشترك بنجاح";
        
        // تنظيف المخرجات المؤقتة
        ob_end_clean();
        header("Location: agents.php");
        exit();
        
    } catch (Exception $e) {
        // التراجع عن المعاملة في حالة حدوث خطأ
        $conn->rollback();
        $error = $e->getMessage();
    }
}


?>



<div class="container-fluid py-4">
    <!-- رأس الصفحة مع أزرار الإجراءات -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="bi bi-person-plus-fill me-2"></i>
            إضافة مشترك جديد
        </h1>
        
        <a href="agents.php" class="btn btn-secondary">
            <i class="bi bi-arrow-right me-1"></i> العودة للقائمة
        </a>
    </div>

    <?php if (isset($error)): ?>
        <div class="alert alert-danger alert-dismissible fade show animate__animated animate__fadeIn" role="alert">
            <i class="bi bi-exclamation-triangle-fill me-2"></i>
            <?php echo $error; ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    <?php endif; ?>

    <form method="POST" action="" id="addAgentForm">
        <!-- بطاقة المعلومات الأساسية -->
        <div class="card mb-4 animate__animated animate__fadeIn">
            <div class="card-header bg-light">
                <h5 class="mb-0">
                    <i class="bi bi-info-circle-fill me-2"></i>
                    المعلومات الأساسية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold required">اسم المشترك</label>
                        <input type="text" name="agent_name" class="form-control" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label fw-bold">رقم الهاتف</label>
                                <input type="text" name="phone_number" class="form-control" value="<?php echo isset($_POST['phone_number']) ? htmlspecialchars($_POST['phone_number']) : ''; ?>">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label fw-bold">رقم الهاتف الثانوي</label>
                                <input type="text" name="secondary_phone_number" class="form-control" value="<?php echo isset($_POST['secondary_phone_number']) ? htmlspecialchars($_POST['secondary_phone_number']) : ''; ?>">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">العنوان</label>
                        <input type="text" name="point_name" class="form-control">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">الحالة</label>
                        <select name="unms_status" class="form-select">
                            <option value="active">نشط</option>
                            <option value="inactive">غير نشط</option>
                        </select>
                    </div>
                </div>


            </div>
        </div>

        <!-- بطاقة معلومات الاتصال -->
        <div class="card mb-4 animate__animated animate__fadeIn" style="animation-delay: 0.1s">
            <div class="card-header bg-light">
                <h5 class="mb-0">
                    <i class="bi bi-wifi me-2"></i>
                    معلومات الاتصال
                </h5>
            </div>
            <div class="card-body">
                <!-- قسم عناوين IP -->
                <div class="mb-4">
                    <label class="form-label fw-bold d-flex justify-content-between">
                        <span>عناوين IP</span>
                        <button type="button" class="btn btn-sm btn-primary" id="addIpAddress">
                            <i class="bi bi-plus-circle-fill me-1"></i> إضافة عنوان IP
                        </button>
                    </label>
                    <div id="ipAddressContainer">
                        <!-- سيتم إضافة حقول عناوين IP هنا بواسطة JavaScript -->
                    </div>
                </div>

                <!-- أسماء المستخدمين وكلمات المرور المتعددة -->
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <label class="form-label fw-bold">
                                <i class="bi bi-person-lock me-2"></i>
                                أسماء المستخدمين وكلمات المرور
                            </label>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="addUsernameField()">
                                <i class="bi bi-plus-circle me-1"></i>
                                إضافة اسم مستخدم جديد
                            </button>
                        </div>
                        <div id="usernameContainer">
                            <!-- سيتم إضافة الحقول هنا بواسطة JavaScript -->
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">RP-SUB</label>
                        <input type="text" name="rp_sub" class="form-control">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">MAC Address</label>
                        <input type="text" name="mac_address" class="form-control">
                    </div>
                </div>
            </div>
        </div>

        <!-- بطاقة معلومات الشبكة -->
        <div class="card mb-4 animate__animated animate__fadeIn" style="animation-delay: 0.2s">
            <div class="card-header bg-light">
                <h5 class="mb-0">
                    <i class="bi bi-hdd-network-fill me-2"></i>
                    معلومات الشبكة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">المنفذ</label>
                        <input type="text" name="port" class="form-control">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">SSID</label>
                        <input type="text" name="ssid" class="form-control">
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">ملكية الجهاز</label>
                        <input type="text" name="device_ownership" class="form-control">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">ملاحظات على المشترك</label>
                        <textarea name="notes" class="form-control" rows="3"></textarea>
                    </div>
                </div>
            </div>
        </div>



        <!-- معلومات إضافية -->
        <div class="card mb-4 animate__animated animate__fadeIn" style="animation-delay: 0.3s">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">
                    <i class="bi bi-plus-circle-fill me-2"></i>
                    معلومات إضافية
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">تسلسل</label>
                        <input type="text" name="serial_number" class="form-control" value="<?php echo isset($_POST['serial_number']) ? htmlspecialchars($_POST['serial_number']) : ''; ?>">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">SN ONU</label>
                        <input type="text" name="sn_onu" class="form-control" value="<?php echo isset($_POST['sn_onu']) ? htmlspecialchars($_POST['sn_onu']) : ''; ?>">
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">رقم السكتر</label>
                        <input type="text" name="sector_number" class="form-control" value="<?php echo isset($_POST['sector_number']) ? htmlspecialchars($_POST['sector_number']) : ''; ?>">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">سعر الاشتراك</label>
                        <div class="input-group">
                            <input type="number" step="0.01" name="bill_price" class="form-control" value="<?php echo isset($_POST['bill_price']) ? htmlspecialchars($_POST['bill_price']) : ''; ?>">
                            <span class="input-group-text">دينار</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- القوائم المنسدلة -->
        <div class="card mb-4 animate__animated animate__fadeIn" style="animation-delay: 0.35s">
            <div class="card-header bg-secondary text-white">
                <h5 class="mb-0">
                    <i class="bi bi-list-ul me-2"></i>
                    خيارات النظام
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">Port</label>
                        <select name="port_id" class="form-select">
                            <option value="">اختر Port</option>
                            <?php
                            $ports_result = $conn->query("SELECT id, name FROM ports WHERE status = 'active' ORDER BY name");
                            if ($ports_result) {
                                while ($port = $ports_result->fetch_assoc()) {
                                    $selected = (isset($_POST['port_id']) && $_POST['port_id'] == $port['id']) ? 'selected' : '';
                                    echo "<option value='{$port['id']}' $selected>" . htmlspecialchars($port['name']) . "</option>";
                                }
                            }
                            ?>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">مكان كابينة</label>
                        <select name="cabinet_location_id" class="form-select">
                            <option value="">اختر مكان كابينة</option>
                            <?php
                            $cabinets_result = $conn->query("SELECT id, name FROM cabinet_locations WHERE status = 'active' ORDER BY name");
                            if ($cabinets_result) {
                                while ($cabinet = $cabinets_result->fetch_assoc()) {
                                    $selected = (isset($_POST['cabinet_location_id']) && $_POST['cabinet_location_id'] == $cabinet['id']) ? 'selected' : '';
                                    echo "<option value='{$cabinet['id']}' $selected>" . htmlspecialchars($cabinet['name']) . "</option>";
                                }
                            }
                            ?>
                        </select>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">نوع الخدمة</label>
                        <select name="discount_id" class="form-select">
                            <option value="">اختر نوع الخدمة</option>
                            <option value="wifi" <?php echo (isset($_POST['discount_id']) && $_POST['discount_id'] == 'wifi') ? 'selected' : ''; ?>>WiFi</option>
                            <option value="pppoe" <?php echo (isset($_POST['discount_id']) && $_POST['discount_id'] == 'pppoe') ? 'selected' : ''; ?>>PPPoE</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">باقة الخدمة</label>
                        <select name="service_type_id" class="form-select">
                            <option value="">اختر باقة الخدمة</option>
                            <?php
                            $services_result = $conn->query("SELECT id, name FROM service_types WHERE status = 'active' ORDER BY name");
                            if ($services_result) {
                                while ($service = $services_result->fetch_assoc()) {
                                    $selected = (isset($_POST['service_type_id']) && $_POST['service_type_id'] == $service['id']) ? 'selected' : '';
                                    echo "<option value='{$service['id']}' $selected>" . htmlspecialchars($service['name']) . "</option>";
                                }
                            }
                            ?>
                        </select>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">ملكية الجهاز</label>
                        <select name="device_ownership_id" class="form-select">
                            <option value="">اختر ملكية الجهاز</option>
                            <?php
                            $ownership_result = $conn->query("SELECT id, name FROM device_ownership WHERE status = 'active' ORDER BY name");
                            if ($ownership_result) {
                                while ($ownership = $ownership_result->fetch_assoc()) {
                                    $selected = (isset($_POST['device_ownership_id']) && $_POST['device_ownership_id'] == $ownership['id']) ? 'selected' : '';
                                    echo "<option value='{$ownership['id']}' $selected>" . htmlspecialchars($ownership['name']) . "</option>";
                                }
                            }
                            ?>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">تابع الى</label>
                        <select name="belongs_to_id" class="form-select">
                            <option value="">اختر تابع الى</option>
                            <?php
                            $belongs_result = $conn->query("SELECT id, name FROM belongs_to_options WHERE status = 'active' ORDER BY name");
                            if ($belongs_result) {
                                while ($belongs = $belongs_result->fetch_assoc()) {
                                    $selected = (isset($_POST['belongs_to_id']) && $_POST['belongs_to_id'] == $belongs['id']) ? 'selected' : '';
                                    echo "<option value='{$belongs['id']}' $selected>" . htmlspecialchars($belongs['name']) . "</option>";
                                }
                            }
                            ?>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">اسم الفرع</label>
                        <select name="branch_name_id" class="form-select">
                            <option value="">اختر اسم الفرع</option>
                            <?php
                            $branches_result = $conn->query("SELECT id, name FROM branch_names WHERE status = 'active' ORDER BY name");
                            if ($branches_result) {
                                while ($branch = $branches_result->fetch_assoc()) {
                                    $selected = (isset($_POST['branch_name_id']) && $_POST['branch_name_id'] == $branch['id']) ? 'selected' : '';
                                    echo "<option value='{$branch['id']}' $selected>" . htmlspecialchars($branch['name']) . "</option>";
                                }
                            }
                            ?>
                        </select>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">مكان البرج</label>
                        <select name="tower_location_id" class="form-select">
                            <option value="">اختر مكان البرج</option>
                            <?php
                            $towers_result = $conn->query("SELECT id, name FROM tower_locations WHERE status = 'active' ORDER BY name");
                            if ($towers_result) {
                                while ($tower = $towers_result->fetch_assoc()) {
                                    $selected = (isset($_POST['tower_location_id']) && $_POST['tower_location_id'] == $tower['id']) ? 'selected' : '';
                                    echo "<option value='{$tower['id']}' $selected>" . htmlspecialchars($tower['name']) . "</option>";
                                }
                            }
                            ?>
                        </select>
                    </div>
                </div>

                <!-- قسم الموقع الجغرافي -->
                <div class="row">
                    <div class="col-12 mb-3">
                        <h5 class="text-primary">
                            <i class="bi bi-geo-alt me-2"></i>
                            الموقع الجغرافي
                        </h5>
                        <hr>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4 mb-3">
                        <label class="form-label fw-bold">خط العرض (Latitude)</label>
                        <input type="text" class="form-control" name="latitude"
                               id="latitude" placeholder="مثال: 31.985510750149068"
                               pattern="^-?([0-9]{1,3}(\.[0-9]{1,15})?|180(\.0{1,15})?)$"
                               title="أدخل خط العرض بالدرجات العشرية (مثال: 31.985510750149068)"
                               value="<?php echo isset($_POST['latitude']) ? htmlspecialchars($_POST['latitude']) : ''; ?>">
                        <small class="text-muted">خط العرض بالدرجات العشرية</small>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label fw-bold">خط الطول (Longitude)</label>
                        <input type="text" class="form-control" name="longitude"
                               id="longitude" placeholder="مثال: 44.35785272883488"
                               pattern="^-?([0-9]{1,3}(\.[0-9]{1,15})?|180(\.0{1,15})?)$"
                               title="أدخل خط الطول بالدرجات العشرية (مثال: 44.35785272883488)"
                               value="<?php echo isset($_POST['longitude']) ? htmlspecialchars($_POST['longitude']) : ''; ?>">
                        <small class="text-muted">خط الطول بالدرجات العشرية</small>
                    </div>
                    <div class="col-md-4 mb-3">
                        <label class="form-label fw-bold">تحديد الموقع</label>
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-outline-primary" onclick="getCurrentLocation()">
                                <i class="bi bi-geo-alt-fill me-2"></i>
                                الموقع الحالي
                            </button>
                            <button type="button" class="btn btn-outline-info" onclick="openMapSelector()">
                                <i class="bi bi-map me-2"></i>
                                اختيار من الخريطة
                            </button>
                        </div>
                    </div>
                </div>

                <!-- عرض الموقع على الخريطة -->
                <div class="row" id="mapPreview" style="display: none;">
                    <div class="col-12 mb-3">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="bi bi-map me-2"></i>
                                    معاينة الموقع
                                </h6>
                            </div>
                            <div class="card-body">
                                <div id="previewMap" style="height: 300px; border-radius: 8px;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- أزرار الإرسال -->
        <div class="d-flex justify-content-center mb-4 animate__animated animate__fadeIn" style="animation-delay: 0.4s">
            <button type="submit" class="btn btn-primary btn-lg px-5 me-2">
                <i class="bi bi-check-circle-fill me-2"></i> حفظ البيانات
            </button>
            <a href="agents.php" class="btn btn-secondary btn-lg px-5">
                <i class="bi bi-x-circle-fill me-2"></i> إلغاء
            </a>
        </div>
    </form>
</div>

<style>
/* تحسين مظهر حقول أسماء المستخدمين */
.username-row {
    border: 1px solid #e3e6f0;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.username-row:hover {
    border-color: #5a5c69;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.username-row .card-body {
    padding: 1rem;
}

.username-row .form-floating label {
    color: #5a5c69;
    font-weight: 500;
}

.username-row .form-control:focus {
    border-color: #4e73df;
    box-shadow: 0 0 0 0.2rem rgba(78, 115, 223, 0.25);
}

.primary-user-radio:checked + label {
    color: #1cc88a;
    font-weight: bold;
}

.remove-username {
    height: 38px;
}

/* تحسين مظهر عناوين IP */
.ip-address-row {
    border: 1px solid #e3e6f0;
    border-radius: 10px;
    transition: all 0.3s ease;
}

.ip-address-row:hover {
    border-color: #5a5c69;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    
    // إضافة عنوان IP
    let ipCounter = 0;
    
    document.getElementById('addIpAddress').addEventListener('click', function() {
        addIpAddressField();
    });
    
    function addIpAddressField() {
        const container = document.getElementById('ipAddressContainer');
        const row = document.createElement('div');
        row.className = 'card mb-2 ip-address-row';
        row.innerHTML = `
            <div class="card-body">
                <div class="row mb-2">
                    <div class="col-md-5">
                        <div class="form-floating mb-2">
                            <input type="text" name="ip_addresses[${ipCounter}][address]" class="form-control" id="ipAddress${ipCounter}" placeholder="عنوان IP">
                            <label for="ipAddress${ipCounter}">عنوان IP</label>
                        </div>
                    </div>
                    <div class="col-md-5">
                        <div class="form-floating mb-2">
                            <input type="text" name="ip_addresses[${ipCounter}][description]" class="form-control" id="ipDescription${ipCounter}" placeholder="وصف العنوان">
                            <label for="ipDescription${ipCounter}">وصف العنوان (اختياري)</label>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <div class="d-flex h-100 align-items-center justify-content-around">
                            <div class="form-check">
                                <input class="form-check-input primary-ip-radio" type="radio" name="primary_ip" id="primaryIp${ipCounter}" value="${ipCounter}">
                                <label class="form-check-label" for="primaryIp${ipCounter}">
                                    رئيسي
                                </label>
                            </div>
                            <button type="button" class="btn btn-outline-danger btn-sm remove-ip" data-index="${ipCounter}">
                                <i class="bi bi-trash-fill"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <!-- حقول اسم المستخدم وكلمة المرور -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-floating mb-2">
                            <input type="text" name="ip_addresses[${ipCounter}][username]" class="form-control" id="ipUsername${ipCounter}" placeholder="اسم المستخدم">
                            <label for="ipUsername${ipCounter}">اسم المستخدم</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="input-group">
                            <div class="form-floating flex-grow-1">
                                <input type="password" name="ip_addresses[${ipCounter}][password]" class="form-control" id="ipPassword${ipCounter}" placeholder="كلمة المرور">
                                <label for="ipPassword${ipCounter}">كلمة المرور</label>
                            </div>
                            <button class="btn btn-outline-secondary" type="button" onclick="toggleIpPassword(${ipCounter})">
                                <i class="bi bi-eye-fill" id="ipPasswordToggleIcon${ipCounter}"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        container.appendChild(row);
        
        // تحديد أول عنوان IP كعنوان رئيسي افتراضيًا
        if (ipCounter === 0) {
            document.getElementById(`primaryIp${ipCounter}`).checked = true;
        }
        
        // إضافة حدث لزر الحذف
        row.querySelector('.remove-ip').addEventListener('click', function() {
            container.removeChild(row);
            
            // إذا تم حذف العنوان الرئيسي، حدد أول عنوان متبقي كرئيسي
            const wasChecked = this.parentNode.querySelector('input[type="radio"]').checked;
            if (wasChecked) {
                const firstRadio = document.querySelector('.primary-ip-radio');
                if (firstRadio) {
                    firstRadio.checked = true;
                }
            }
        });
        
        ipCounter++;
    }
    
    // إضافة أول حقل عنوان IP افتراضيًا
    addIpAddressField();

    // إضافة أول حقل اسم مستخدم افتراضيًا
    addUsernameField();
});


// دالة لإظهار/إخفاء كلمة المرور لعناوين IP
function toggleIpPassword(index) {
    const passwordField = document.getElementById(`ipPassword${index}`);
    const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
    passwordField.setAttribute('type', type);

    // تغيير أيقونة الزر
    const icon = document.getElementById(`ipPasswordToggleIcon${index}`);
    if (type === 'text') {
        icon.classList.remove('bi-eye-fill');
        icon.classList.add('bi-eye-slash-fill');
    } else {
        icon.classList.remove('bi-eye-slash-fill');
        icon.classList.add('bi-eye-fill');
    }
}

// متغير لعد أسماء المستخدمين
let usernameCounter = 0;

// دالة لإضافة حقل اسم مستخدم جديد
function addUsernameField() {
    const container = document.getElementById('usernameContainer');
    const row = document.createElement('div');
    row.className = 'card mb-2 username-row';
    row.innerHTML = `
        <div class="card-body">
            <div class="row">
                <div class="col-md-5 mb-2">
                    <div class="form-floating">
                        <input type="text" name="usernames[${usernameCounter}][username]" class="form-control" id="username${usernameCounter}" placeholder="اسم المستخدم">
                        <label for="username${usernameCounter}">اسم المستخدم</label>
                    </div>
                </div>
                <div class="col-md-5 mb-2">
                    <div class="input-group">
                        <div class="form-floating flex-grow-1">
                            <input type="password" name="usernames[${usernameCounter}][password]" class="form-control" id="userPassword${usernameCounter}" placeholder="كلمة المرور">
                            <label for="userPassword${usernameCounter}">كلمة المرور</label>
                        </div>
                        <button class="btn btn-outline-secondary" type="button" onclick="toggleUserPassword(${usernameCounter})">
                            <i class="bi bi-eye-fill" id="userPasswordToggleIcon${usernameCounter}"></i>
                        </button>
                    </div>
                </div>
                <div class="col-md-2 mb-2 d-flex align-items-center">
                    <div class="form-check me-2">
                        <input class="form-check-input primary-user-radio" type="radio" name="primary_user" value="${usernameCounter}" id="primaryUser${usernameCounter}">
                        <label class="form-check-label" for="primaryUser${usernameCounter}">
                            رئيسي
                        </label>
                    </div>
                    <button type="button" class="btn btn-outline-danger btn-sm remove-username" title="حذف">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
        </div>
    `;

    container.appendChild(row);

    // تحديد أول اسم مستخدم كرئيسي افتراضيًا
    if (usernameCounter === 0) {
        document.getElementById(`primaryUser${usernameCounter}`).checked = true;
    }

    // إضافة حدث لزر الحذف
    row.querySelector('.remove-username').addEventListener('click', function() {
        container.removeChild(row);

        // إذا تم حذف المستخدم الرئيسي، حدد أول مستخدم متبقي كرئيسي
        const wasChecked = this.parentNode.querySelector('input[type="radio"]').checked;
        if (wasChecked) {
            const firstRadio = document.querySelector('.primary-user-radio');
            if (firstRadio) {
                firstRadio.checked = true;
            }
        }
    });

    usernameCounter++;
}

// دالة لإظهار/إخفاء كلمة المرور لأسماء المستخدمين
function toggleUserPassword(index) {
    const passwordField = document.getElementById(`userPassword${index}`);
    const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
    passwordField.setAttribute('type', type);

    // تغيير أيقونة الزر
    const icon = document.getElementById(`userPasswordToggleIcon${index}`);
    if (type === 'text') {
        icon.classList.remove('bi-eye-fill');
        icon.classList.add('bi-eye-slash-fill');
    } else {
        icon.classList.remove('bi-eye-slash-fill');
        icon.classList.add('bi-eye-fill');
    }
}

// وظائف الموقع الجغرافي
let previewMap = null;
let previewMarker = null;

// الحصول على الموقع الحالي
function getCurrentLocation() {
    if (navigator.geolocation) {
        navigator.geolocation.getCurrentPosition(
            function(position) {
                const lat = position.coords.latitude;
                const lng = position.coords.longitude;

                document.getElementById('latitude').value = lat;
                document.getElementById('longitude').value = lng;

                updateMapPreview(lat, lng);

                // إظهار رسالة نجاح
                showLocationMessage('تم تحديد الموقع الحالي بنجاح', 'success');
            },
            function(error) {
                let message = 'فشل في تحديد الموقع: ';
                switch(error.code) {
                    case error.PERMISSION_DENIED:
                        message += 'تم رفض الإذن للوصول للموقع';
                        break;
                    case error.POSITION_UNAVAILABLE:
                        message += 'معلومات الموقع غير متوفرة';
                        break;
                    case error.TIMEOUT:
                        message += 'انتهت مهلة طلب الموقع';
                        break;
                    default:
                        message += 'خطأ غير معروف';
                        break;
                }
                showLocationMessage(message, 'error');
            },
            {
                enableHighAccuracy: true,
                timeout: 10000,
                maximumAge: 60000
            }
        );
    } else {
        showLocationMessage('المتصفح لا يدعم تحديد الموقع الجغرافي', 'error');
    }
}

// فتح نافذة اختيار الموقع من الخريطة
function openMapSelector() {
    // فتح صفحة الخرائط في نافذة جديدة
    const mapWindow = window.open('../pages/maps.php?mode=select', 'mapSelector', 'width=1000,height=700,scrollbars=yes,resizable=yes');

    // الاستماع لرسائل من نافذة الخريطة
    window.addEventListener('message', function(event) {
        if (event.data && event.data.type === 'locationSelected') {
            document.getElementById('latitude').value = event.data.lat;
            document.getElementById('longitude').value = event.data.lng;

            updateMapPreview(event.data.lat, event.data.lng);
            showLocationMessage('تم اختيار الموقع من الخريطة بنجاح', 'success');

            // إغلاق نافذة الخريطة
            mapWindow.close();
        }
    });
}

// تحديث معاينة الخريطة
function updateMapPreview(lat, lng) {
    const mapPreview = document.getElementById('mapPreview');
    mapPreview.style.display = 'block';

    // إنشاء الخريطة إذا لم تكن موجودة
    if (!previewMap) {
        // تحميل Leaflet إذا لم يكن محملاً
        if (typeof L === 'undefined') {
            loadLeaflet(() => {
                initPreviewMap(lat, lng);
            });
        } else {
            initPreviewMap(lat, lng);
        }
    } else {
        // تحديث الخريطة الموجودة
        previewMap.setView([lat, lng], 15);

        if (previewMarker) {
            previewMarker.setLatLng([lat, lng]);
        } else {
            previewMarker = L.marker([lat, lng]).addTo(previewMap);
        }
    }
}

// تحميل مكتبة Leaflet
function loadLeaflet(callback) {
    // تحميل CSS
    const link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css';
    document.head.appendChild(link);

    // تحميل JavaScript
    const script = document.createElement('script');
    script.src = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js';
    script.onload = callback;
    document.head.appendChild(script);
}

// إنشاء خريطة المعاينة
function initPreviewMap(lat, lng) {
    previewMap = L.map('previewMap').setView([lat, lng], 15);

    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(previewMap);

    previewMarker = L.marker([lat, lng]).addTo(previewMap);
}

// إظهار رسائل الموقع
function showLocationMessage(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const icon = type === 'success' ? 'bi-check-circle' : 'bi-exclamation-triangle';

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert ${alertClass} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        <i class="bi ${icon} me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // إدراج الرسالة قبل النموذج
    const form = document.querySelector('form');
    form.parentNode.insertBefore(alertDiv, form);

    // إزالة الرسالة تلقائياً بعد 5 ثوان
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// التحقق من صحة الإحداثيات
function validateCoordinates(lat, lng) {
    // التحقق من أن القيم أرقام صحيحة
    const latNum = parseFloat(lat);
    const lngNum = parseFloat(lng);

    if (isNaN(latNum) || isNaN(lngNum)) {
        return false;
    }

    // التحقق من النطاق الصحيح للإحداثيات
    if (latNum < -90 || latNum > 90) {
        return false;
    }

    if (lngNum < -180 || lngNum > 180) {
        return false;
    }

    return true;
}

// مراقبة تغيير حقول الموقع يدوياً
document.addEventListener('DOMContentLoaded', function() {
    const latInput = document.getElementById('latitude');
    const lngInput = document.getElementById('longitude');
    const form = document.querySelector('form');

    function handleLocationChange() {
        const lat = latInput.value.trim();
        const lng = lngInput.value.trim();

        if (lat && lng && validateCoordinates(lat, lng)) {
            updateMapPreview(parseFloat(lat), parseFloat(lng));
            // إزالة رسائل الخطأ السابقة
            latInput.classList.remove('is-invalid');
            lngInput.classList.remove('is-invalid');
        }
    }

    // التحقق عند فقدان التركيز
    latInput.addEventListener('blur', handleLocationChange);
    lngInput.addEventListener('blur', handleLocationChange);

    // التحقق عند إرسال النموذج
    form.addEventListener('submit', function(e) {
        const lat = latInput.value.trim();
        const lng = lngInput.value.trim();

        // إذا كانت الحقول فارغة، السماح بالإرسال
        if (!lat && !lng) {
            return true;
        }

        // إذا كان أحد الحقول فارغ والآخر ممتلئ
        if ((lat && !lng) || (!lat && lng)) {
            e.preventDefault();
            showLocationMessage('يجب إدخال كلا من خط العرض وخط الطول أو تركهما فارغين', 'error');
            return false;
        }

        // التحقق من صحة الإحداثيات
        if (lat && lng && !validateCoordinates(lat, lng)) {
            e.preventDefault();
            latInput.classList.add('is-invalid');
            lngInput.classList.add('is-invalid');
            showLocationMessage('الإحداثيات غير صحيحة. خط العرض يجب أن يكون بين -90 و 90، وخط الطول بين -180 و 180', 'error');
            return false;
        }
    });
});
</script>

<?php include '../includes/footer.php'; ?>
