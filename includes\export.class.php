<?php
/**
 * Export Manager Class - إدارة تصدير البيانات
 * نسخة مبسطة ومحسنة للتصدير
 */

class ExportManager {
    private $conn;
    
    public function __construct($connection) {
        $this->conn = $connection;
    }
    
    /**
     * تصدير البيانات إلى ملف CSV
     */
    public function exportToCSV($type = 'basic', $filters = []) {
        try {
            // إنشاء مجلد التصدير إذا لم يكن موجوداً
            $export_dir = dirname(__DIR__) . '/exports';
            if (!is_dir($export_dir)) {
                mkdir($export_dir, 0755, true);
            }
            
            // إنشاء اسم الملف
            $filename = 'agents_export_' . $type . '_' . date('Y-m-d_H-i-s') . '.csv';
            $filepath = $export_dir . '/' . $filename;
            
            // فتح الملف للكتابة
            $fp = fopen($filepath, 'w');
            if ($fp === false) {
                throw new Exception("Could not create export file");
            }
            
            // إضافة BOM لدعم العربية في Excel
            fprintf($fp, chr(0xEF).chr(0xBB).chr(0xBF));
            
            // تحديد الأعمدة والاستعلام حسب النوع
            $columns = $this->getColumnsForType($type);
            $sql = $this->getSQLForType($type, $filters);
            
            // كتابة رؤوس الأعمدة
            fputcsv($fp, array_values($columns));
            
            // تنفيذ الاستعلام
            $result = $this->conn->query($sql);
            if (!$result) {
                throw new Exception("Database query failed: " . $this->conn->error);
            }
            
            // كتابة البيانات
            $count = 0;
            while ($row = $result->fetch_assoc()) {
                $exportRow = [];
                foreach ($columns as $field => $title) {
                    $value = $row[$field] ?? '';
                    $value = $this->formatValue($field, $value);
                    $exportRow[] = $value;
                }
                fputcsv($fp, $exportRow);
                $count++;
            }
            
            fclose($fp);
            
            if ($count === 0) {
                unlink($filepath);
                throw new Exception("No data found for export");
            }
            
            return $filename;
            
        } catch (Exception $e) {
            if (isset($fp) && $fp) {
                fclose($fp);
            }
            if (isset($filepath) && file_exists($filepath)) {
                unlink($filepath);
            }
            throw $e;
        }
    }
    
    /**
     * الحصول على الأعمدة حسب نوع التصدير
     */
    private function getColumnsForType($type) {
        switch ($type) {
            case 'basic':
                return [
                    'agent_name' => 'اسم المشترك',
                    'phone_number' => 'رقم الهاتف',
                    'point_name' => 'العنوان',
                    'unms_status' => 'الحالة',
                    'created_at' => 'تاريخ الإنشاء'
                ];
                
            case 'detailed':
                return [
                    'agent_name' => 'اسم المشترك',
                    'phone_number' => 'رقم الهاتف',
                    'secondary_phone_number' => 'رقم الهاتف الثانوي',
                    'point_name' => 'العنوان',
                    'unms_status' => 'الحالة',
                    'username' => 'اسم المستخدم',
                    'rp_sub' => 'RP-SUB',
                    'mac_address' => 'MAC Address',
                    'port' => 'المنفذ',
                    'ssid' => 'SSID',
                    'device_ownership' => 'ملكية الجهاز',
                    'serial_number' => 'تسلسل',
                    'sn_onu' => 'SN ONU',
                    'sector_number' => 'رقم السكتر',
                    'bill_price' => 'سعر فواتير',
                    'notes' => 'ملاحظات',
                    'created_at' => 'تاريخ الإنشاء'
                ];
                
            case 'financial':
                return [
                    'agent_name' => 'اسم المشترك',
                    'phone_number' => 'رقم الهاتف',
                    'bill_price' => 'سعر فواتير',
                    'unms_status' => 'الحالة',
                    'created_at' => 'تاريخ الإنشاء'
                ];
                
            default:
                throw new Exception("Invalid export type: " . $type);
        }
    }
    
    /**
     * الحصول على الاستعلام حسب نوع التصدير
     */
    private function getSQLForType($type, $filters = []) {
        $columns = array_keys($this->getColumnsForType($type));
        $select = implode(', ', $columns);
        
        $sql = "SELECT {$select} FROM agents WHERE 1=1";
        
        // إضافة الفلاتر
        if (!empty($filters['status'])) {
            $sql .= " AND unms_status = '" . $this->conn->real_escape_string($filters['status']) . "'";
        }
        
        $sql .= " ORDER BY created_at DESC";
        
        return $sql;
    }
    
    /**
     * تنسيق القيم للتصدير
     */
    private function formatValue($field, $value) {
        // تنسيق التواريخ
        if ($field === 'created_at' && $value) {
            return date('Y-m-d H:i', strtotime($value));
        }
        
        // تنسيق الحالة
        if ($field === 'unms_status') {
            return $value === 'active' ? 'نشط' : 'غير نشط';
        }
        
        // تنسيق السعر
        if ($field === 'bill_price' && $value) {
            return number_format($value, 2) . ' دينار';
        }
        
        return $value;
    }
    
    /**
     * الحصول على إحصائيات التصدير
     */
    public function getExportStats() {
        $stats = [
            'total' => 0,
            'active' => 0,
            'inactive' => 0
        ];
        
        try {
            // إجمالي المشتركين
            $result = $this->conn->query("SELECT COUNT(*) as count FROM agents");
            if ($result) {
                $stats['total'] = $result->fetch_assoc()['count'] ?? 0;
            }
            
            // المشتركين النشطين
            $result = $this->conn->query("SELECT COUNT(*) as count FROM agents WHERE unms_status = 'active'");
            if ($result) {
                $stats['active'] = $result->fetch_assoc()['count'] ?? 0;
            }
            
            // المشتركين غير النشطين
            $stats['inactive'] = $stats['total'] - $stats['active'];
            
        } catch (Exception $e) {
            error_log("Error fetching export stats: " . $e->getMessage());
        }
        
        return $stats;
    }
    
    /**
     * تنظيف ملفات التصدير القديمة
     */
    public function cleanOldExports($days = 7) {
        $export_dir = dirname(__DIR__) . '/exports';
        if (!is_dir($export_dir)) {
            return;
        }
        
        $files = glob($export_dir . '/agents_export_*.csv');
        $cutoff = time() - ($days * 24 * 60 * 60);
        
        foreach ($files as $file) {
            if (filemtime($file) < $cutoff) {
                unlink($file);
            }
        }
    }
}
?>
