<?php
require_once '../includes/init.php';
$page_title = "لوحة مراقبة المشتركين";
include '../includes/header.php';
?>

<div class="container-fluid px-4">
    <h1 class="mt-4">لوحة مراقبة المشتركين</h1>
    
    <!-- إحصائيات عامة -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <h4 class="mb-0" id="total-agents">-</h4>
                    <div>إجمالي المشتركين</div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <h4 class="mb-0" id="online-agents">-</h4>
                    <div>المشتركين المتصلين</div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-danger text-white mb-4">
                <div class="card-body">
                    <h4 class="mb-0" id="offline-agents">-</h4>
                    <div>المشتركين غير المتصلين</div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-info text-white mb-4">
                <div class="card-body">
                    <h4 class="mb-0" id="avg-uptime">-</h4>
                    <div>متوسط التوفر</div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول المشتركين -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-table me-1"></i>
            حالة المشتركين
        </div>
        <div class="card-body">
            <table id="agentsStatusTable" class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>المشترك</th>
                        <th>عنوان IP</th>
                        <th>الحالة</th>
                        <th>وقت الاستجابة</th>
                        <th>آخر تحديث</th>
                        <th>نسبة التوفر</th>
                        <th>تفاصيل</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>

    <!-- سجل التغييرات -->
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-history me-1"></i>
            سجل التغييرات
        </div>
        <div class="card-body">
            <table id="statusLogsTable" class="table table-striped table-bordered">
                <thead>
                    <tr>
                        <th>الوقت</th>
                        <th>المشترك</th>
                        <th>الحالة السابقة</th>
                        <th>الحالة الجديدة</th>
                        <th>وقت الاستجابة</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Modal تفاصيل المشترك -->
<div class="modal fade" id="agentDetailsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل المشترك</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="uptimeChart"></div>
                <div id="responseTimeChart"></div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // دالة لتحديث الإحصائيات
    function updateStats() {
        console.log('Fetching stats...'); // للتصحيح
        $.ajax({
            url: 'api/get_monitoring_stats.php',
            method: 'GET',
            success: function(data) {
                console.log('Raw stats data:', data); // للتصحيح
                if (data.error) {
                    console.error('Server error:', data.error);
                    return;
                }
                $('#total-agents').text(data.total_agents || 0);
                $('#online-agents').text(data.online_agents || 0);
                $('#offline-agents').text(data.offline_agents || 0);
                $('#avg-uptime').text(formatUptime(data.avg_uptime || 0));
            },
            error: function(xhr, status, error) {
                console.error('AJAX error:', error);
                console.error('Status:', status);
                console.error('Response:', xhr.responseText);
            }
        });
    }

    // تهيئة جدول حالة المشتركين
    var agentsTable = $('#agentsStatusTable').DataTable({
        ajax: {
            url: 'api/get_agents_status.php',
            dataSrc: function(json) {
                console.log('Raw agents data:', json); // للتصحيح
                if (json.error) {
                    console.error('Server error:', json.error);
                    return [];
                }
                return json || [];
            },
            error: function(xhr, status, error) {
                console.error('AJAX error:', error);
                console.error('Status:', status);
                console.error('Response:', xhr.responseText);
                return [];
            }
        },
        columns: [
            { data: 'agent_name' },
            { data: 'ip_address' },
            { 
                data: 'status',
                render: function(data) {
                    return getStatusBadge(data);
                }
            },
            { 
                data: 'response_time',
                render: function(data) {
                    return formatResponseTime(data);
                }
            },
            { 
                data: 'last_check',
                render: function(data) {
                    return formatArabicDate(data);
                }
            },
            { 
                data: 'uptime',
                render: function(data) {
                    return formatUptime(data);
                }
            },
            {
                data: 'agent_id',
                render: function(data) {
                    return '<button class="btn btn-sm btn-info" onclick="showAgentDetails(' + data + ')">تفاصيل</button>';
                }
            }
        ],
        order: [[4, 'desc']],
        language: {
            url: '../assets/js/dataTables.arabic.json'
        }
    });

    // تهيئة جدول سجل التغييرات
    var logsTable = $('#statusLogsTable').DataTable({
        ajax: {
            url: 'api/get_status_logs.php',
            dataSrc: ''
        },
        columns: [
            { 
                data: 'log_time',
                render: function(data) {
                    return formatArabicDate(data);
                }
            },
            { data: 'agent_name' },
            { 
                data: 'old_status',
                render: function(data) {
                    return getStatusBadge(data);
                }
            },
            { 
                data: 'new_status',
                render: function(data) {
                    return getStatusBadge(data);
                }
            },
            { 
                data: 'response_time',
                render: function(data) {
                    return formatResponseTime(data);
                }
            }
        ],
        order: [[0, 'desc']],
        language: {
            url: '../assets/js/dataTables.arabic.json'
        }
    });

    // تحديث البيانات كل دقيقة
    setInterval(function() {
        updateStats();
        agentsTable.ajax.reload(null, false);
        logsTable.ajax.reload(null, false);
    }, 60000);

    // التحديث الأولي
    updateStats();
});

// عرض تفاصيل المشترك
function showAgentDetails(agentId) {
    $.get('api/get_agent_details.php', { agent_id: agentId }, function(data) {
        $('#agentDetailsModal .modal-title').text('تفاصيل المشترك: ' + data.agent_name);
        // تحديث محتوى Modal هنا
        $('#agentDetailsModal').modal('show');
    });
}

// دالة لتنسيق وقت الاستجابة
function formatResponseTime(time) {
    return time ? time + ' ms' : '-';
}

// دالة لتنسيق نسبة التوفر
function formatUptime(uptime) {
    return uptime + '%';
}

// دالة لتنسيق التاريخ باللغة العربية
function formatArabicDate(date) {
    // قم بتحديث هذه الدالة لتنسيق التاريخ باللغة العربية
    return date;
}

// دالة لتنسيق شارة الحالة
function getStatusBadge(status) {
    return status === 'online' ? 
        '<span class="badge bg-success">متصل</span>' : 
        '<span class="badge bg-danger">غير متصل</span>';
}
</script>

<?php include '../includes/footer.php'; ?>
