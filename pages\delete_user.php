<?php
require_once '../includes/init.php';

// التحقق من الصلاحيات
if (!isset($_SESSION['user_id'])) {
    header("Location: ../index.php");
    exit();
}

// التحقق من صلاحيات إدارة المستخدمين
$stmt = $conn->prepare("SELECT role FROM users WHERE id = ?");
$stmt->bind_param("i", $_SESSION['user_id']);
$stmt->execute();
$result = $stmt->get_result();
$current_user = $result->fetch_assoc();

if ($current_user['role'] !== 'admin') {
    $_SESSION['error'] = "ليس لديك صلاحية حذف المستخدمين";
    header("Location: manage_users.php");
    exit();
}

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['user_id'])) {
    try {
        $user_id = (int)$_POST['user_id'];
        
        // التحقق من أن المستخدم ليس مديراً
        $check = $conn->prepare("SELECT role FROM users WHERE id = ?");
        $check->bind_param("i", $user_id);
        $check->execute();
        $user = $check->get_result()->fetch_assoc();
        
        if ($user['role'] === 'admin') {
            throw new Exception("لا يمكن حذف حساب مدير");
        }
        
        // حذف المستخدم (سيتم حذف الصلاحيات تلقائياً بسبب ON DELETE CASCADE)
        $stmt = $conn->prepare("DELETE FROM users WHERE id = ?");
        $stmt->bind_param("i", $user_id);
        
        if ($stmt->execute()) {
            $_SESSION['success'] = "تم حذف المستخدم بنجاح";
        } else {
            throw new Exception("فشل في حذف المستخدم");
        }
    } catch (Exception $e) {
        $_SESSION['error'] = $e->getMessage();
    }
}

header("Location: manage_users.php");
exit();
